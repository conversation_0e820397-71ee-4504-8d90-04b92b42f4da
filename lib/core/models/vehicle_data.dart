import 'package:equatable/equatable.dart';
import '../constants/app_constants.dart';

/// Modèle représentant un véhicule détecté
class VehicleData extends Equatable {
  final String id;
  final BoundingBox bbox;
  final double confidence;
  final DateTime timestamp;
  final double? depth;
  final VehicleState state;
  final double? speedKmh;
  final DateTime? portiqueATime;
  final DateTime? portiqueBTime;
  final bool isPhotoCaptured;
  final String? licensePlate;
  final String? driverInfo;
  final int? colorCode; // Code couleur unique pour l'affichage

  const VehicleData({
    required this.id,
    required this.bbox,
    required this.confidence,
    required this.timestamp,
    this.depth,
    this.state = VehicleState.beforeA,
    this.speedKmh,
    this.portiqueATime,
    this.portiqueBTime,
    this.isPhotoCaptured = false,
    this.licensePlate,
    this.driverInfo,
    this.colorCode,
  });

  VehicleData copyWith({
    String? id,
    BoundingBox? bbox,
    double? confidence,
    DateTime? timestamp,
    double? depth,
    VehicleState? state,
    double? speedKmh,
    DateTime? portiqueATime,
    DateTime? portiqueBTime,
    bool? isPhotoCaptured,
    String? licensePlate,
    String? driverInfo,
    int? colorCode,
  }) {
    return VehicleData(
      id: id ?? this.id,
      bbox: bbox ?? this.bbox,
      confidence: confidence ?? this.confidence,
      timestamp: timestamp ?? this.timestamp,
      depth: depth ?? this.depth,
      state: state ?? this.state,
      speedKmh: speedKmh ?? this.speedKmh,
      portiqueATime: portiqueATime ?? this.portiqueATime,
      portiqueBTime: portiqueBTime ?? this.portiqueBTime,
      isPhotoCaptured: isPhotoCaptured ?? this.isPhotoCaptured,
      licensePlate: licensePlate ?? this.licensePlate,
      driverInfo: driverInfo ?? this.driverInfo,
      colorCode: colorCode ?? this.colorCode,
    );
  }

  @override
  List<Object?> get props => [
        id,
        bbox,
        confidence,
        timestamp,
        depth,
        state,
        speedKmh,
        portiqueATime,
        portiqueBTime,
        isPhotoCaptured,
        licensePlate,
        driverInfo,
        colorCode,
      ];
}

/// Modèle représentant une boîte englobante
class BoundingBox extends Equatable {
  final double x;
  final double y;
  final double width;
  final double height;

  const BoundingBox({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Centre de la boîte englobante
  double get centerX => x + width / 2;
  double get centerY => y + height / 2;

  /// Point bottom-center pour l'extraction de profondeur
  double get bottomCenterX => centerX;
  double get bottomCenterY => y + height;

  @override
  List<Object> get props => [x, y, width, height];
}

/// Modèle pour les données de profondeur
class DepthData extends Equatable {
  final List<List<double>> depthMap;
  final List<List<double>> confidenceMap;
  final DateTime timestamp;
  final CameraIntrinsics intrinsics;

  const DepthData({
    required this.depthMap,
    required this.confidenceMap,
    required this.timestamp,
    required this.intrinsics,
  });

  @override
  List<Object> get props => [depthMap, confidenceMap, timestamp, intrinsics];
}

/// Paramètres intrinsèques de la caméra
class CameraIntrinsics extends Equatable {
  final double fx;
  final double fy;
  final double cx;
  final double cy;

  const CameraIntrinsics({
    required this.fx,
    required this.fy,
    required this.cx,
    required this.cy,
  });

  @override
  List<Object> get props => [fx, fy, cx, cy];
}

/// Modèle pour un événement de dépassement de vitesse
class OverspeedEvent extends Equatable {
  final String vehicleId;
  final double measuredSpeed;
  final double speedLimit;
  final DateTime timestamp;
  final String? photoPath;
  final String? licensePlate;
  final double confidence;
  final String? imagePath;
  final Location? location;
  final String? weatherConditions;
  final String? visibility;
  final double? temperature;
  final double? plateConfidence;

  const OverspeedEvent({
    required this.vehicleId,
    required this.measuredSpeed,
    required this.speedLimit,
    required this.timestamp,
    this.photoPath,
    this.licensePlate,
    required this.confidence,
    this.imagePath,
    this.location,
    this.weatherConditions,
    this.visibility,
    this.temperature,
    this.plateConfidence,
  });

  @override
  List<Object?> get props => [
        vehicleId,
        measuredSpeed,
        speedLimit,
        timestamp,
        photoPath,
        licensePlate,
        confidence,
        imagePath,
        location,
        weatherConditions,
        visibility,
        temperature,
        plateConfidence,
      ];
}

/// Localisation GPS
class Location extends Equatable {
  final double latitude;
  final double longitude;

  const Location({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [latitude, longitude];
}

/// Modèle pour les paramètres utilisateur
class UserSettings extends Equatable {
  final double speedLimitKmh;
  final double toleranceKmh;
  final bool autoSaveEnabled;
  final bool blurFacesEnabled;
  final bool blurPlatesEnabled;
  final bool showLicensePlatesEnabled;
  final bool showDriversEnabled;

  const UserSettings({
    this.speedLimitKmh = AppConstants.default_speed_limit_kmh,
    this.toleranceKmh = AppConstants.default_tolerance_kmh,
    this.autoSaveEnabled = AppConstants.default_auto_save,
    this.blurFacesEnabled = false,
    this.blurPlatesEnabled = false,
    this.showLicensePlatesEnabled = false,
    this.showDriversEnabled = false,
  });

  UserSettings copyWith({
    double? speedLimitKmh,
    double? toleranceKmh,
    bool? autoSaveEnabled,
    bool? blurFacesEnabled,
    bool? blurPlatesEnabled,
    bool? showLicensePlatesEnabled,
    bool? showDriversEnabled,
  }) {
    return UserSettings(
      speedLimitKmh: speedLimitKmh ?? this.speedLimitKmh,
      toleranceKmh: toleranceKmh ?? this.toleranceKmh,
      autoSaveEnabled: autoSaveEnabled ?? this.autoSaveEnabled,
      blurFacesEnabled: blurFacesEnabled ?? this.blurFacesEnabled,
      blurPlatesEnabled: blurPlatesEnabled ?? this.blurPlatesEnabled,
      showLicensePlatesEnabled: showLicensePlatesEnabled ?? this.showLicensePlatesEnabled,
      showDriversEnabled: showDriversEnabled ?? this.showDriversEnabled,
    );
  }

  @override
  List<Object> get props => [
        speedLimitKmh,
        toleranceKmh,
        autoSaveEnabled,
        blurFacesEnabled,
        blurPlatesEnabled,
        showLicensePlatesEnabled,
        showDriversEnabled,
      ];
}
