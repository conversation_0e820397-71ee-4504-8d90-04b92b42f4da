import 'package:equatable/equatable.dart';
import 'vehicle_data.dart';

/// Classe pour tracker un véhicule à travers plusieurs frames
class VehicleTracker extends Equatable {
  final String id;
  BoundingBox lastBbox;
  double confidence;
  DateTime timestamp;
  DateTime lastSeen;
  bool isUpdated;
  
  // Historique des positions pour prédiction
  final List<BoundingBox> positionHistory = [];
  final List<DateTime> timeHistory = [];
  
  // Vitesse estimée (pixels par seconde)
  double? velocityX;
  double? velocityY;
  
  VehicleTracker({
    required this.id,
    required BoundingBox initialBbox,
    required double initialConfidence,
    required this.timestamp,
  }) : lastBbox = initialBbox,
       confidence = initialConfidence,
       lastSeen = timestamp,
       isUpdated = true {
    positionHistory.add(initialBbox);
    timeHistory.add(timestamp);
  }
  
  /// Met à jour le tracker avec une nouvelle détection
  void update(BoundingBox newBbox, double newConfidence, DateTime newTimestamp) {
    // Calculer la vitesse si on a assez d'historique
    if (positionHistory.isNotEmpty) {
      final deltaTime = newTimestamp.difference(lastSeen).inMicroseconds / 1000000.0;
      if (deltaTime > 0) {
        velocityX = (newBbox.centerX - lastBbox.centerX) / deltaTime;
        velocityY = (newBbox.centerY - lastBbox.centerY) / deltaTime;
      }
    }
    
    // Mettre à jour les propriétés
    lastBbox = newBbox;
    confidence = newConfidence;
    lastSeen = newTimestamp;
    isUpdated = true;
    
    // Ajouter à l'historique
    positionHistory.add(newBbox);
    timeHistory.add(newTimestamp);
    
    // Limiter la taille de l'historique
    const maxHistorySize = 10;
    if (positionHistory.length > maxHistorySize) {
      positionHistory.removeAt(0);
      timeHistory.removeAt(0);
    }
  }
  
  /// Prédit la position suivante basée sur la vitesse
  BoundingBox? predictNextPosition(Duration deltaTime) {
    if (velocityX == null || velocityY == null) return null;
    
    final deltaSeconds = deltaTime.inMicroseconds / 1000000.0;
    final predictedX = lastBbox.centerX + velocityX! * deltaSeconds;
    final predictedY = lastBbox.centerY + velocityY! * deltaSeconds;
    
    return BoundingBox(
      x: predictedX - lastBbox.width / 2,
      y: predictedY - lastBbox.height / 2,
      width: lastBbox.width,
      height: lastBbox.height,
    );
  }
  
  /// Calcule la confiance du tracking basée sur la cohérence du mouvement
  double getTrackingConfidence() {
    if (positionHistory.length < 3) return confidence;
    
    // Calculer la variance de la vitesse pour évaluer la cohérence
    final velocities = <double>[];
    for (int i = 1; i < positionHistory.length; i++) {
      final deltaTime = timeHistory[i].difference(timeHistory[i-1]).inMicroseconds / 1000000.0;
      if (deltaTime > 0) {
        final dx = positionHistory[i].centerX - positionHistory[i-1].centerX;
        final dy = positionHistory[i].centerY - positionHistory[i-1].centerY;
        final velocity = (dx * dx + dy * dy) / (deltaTime * deltaTime);
        velocities.add(velocity);
      }
    }
    
    if (velocities.isEmpty) return confidence;
    
    // Calculer la variance
    final mean = velocities.reduce((a, b) => a + b) / velocities.length;
    final variance = velocities.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b) / velocities.length;
    
    // Plus la variance est faible, plus le tracking est cohérent
    final consistencyFactor = 1.0 / (1.0 + variance * 1000); // Facteur d'échelle arbitraire
    
    return confidence * consistencyFactor;
  }
  
  /// Vérifie si ce tracker correspond à une nouvelle détection
  bool matches(BoundingBox newBbox, double threshold) {
    // Calculer la distance entre les centres
    final dx = newBbox.centerX - lastBbox.centerX;
    final dy = newBbox.centerY - lastBbox.centerY;
    final distance = dx * dx + dy * dy;
    
    // Calculer la similarité de taille
    final sizeRatio = (newBbox.width * newBbox.height) / (lastBbox.width * lastBbox.height);
    final sizeSimilarity = sizeRatio > 1.0 ? 1.0 / sizeRatio : sizeRatio;
    
    // Combiner distance et similarité de taille
    final matchScore = sizeSimilarity / (1.0 + distance);
    
    return matchScore > threshold;
  }
  
  /// Calcule l'âge du tracker en millisecondes
  int getAge(DateTime currentTime) {
    return currentTime.difference(timestamp).inMilliseconds;
  }
  
  /// Calcule le temps depuis la dernière mise à jour en millisecondes
  int getTimeSinceLastUpdate(DateTime currentTime) {
    return currentTime.difference(lastSeen).inMilliseconds;
  }
  
  @override
  List<Object?> get props => [
    id,
    lastBbox,
    confidence,
    timestamp,
    lastSeen,
    isUpdated,
    velocityX,
    velocityY,
  ];
  
  @override
  String toString() {
    return 'VehicleTracker(id: $id, bbox: $lastBbox, confidence: ${confidence.toStringAsFixed(2)}, velocity: (${velocityX?.toStringAsFixed(3)}, ${velocityY?.toStringAsFixed(3)}))';
  }
}
