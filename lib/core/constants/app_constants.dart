/// Constantes de l'application pour la mesure de vitesse
class AppConstants {
  // Constantes de profondeur (en mètres)
  static const double portique_A_distance = 20.0;
  static const double portique_B_distance = 10.0;
  static const double distance_between_portiques = 10.0;
  
  // Hystérésis pour la détection de franchissement
  static const double hysteresis_threshold = 0.3;
  static const double portique_A_threshold = 19.7;
  static const double portique_B_threshold = 9.7;
  
  // Paramètres de performance
  static const int target_fps = 60;
  static const int max_fps = 120;
  
  // Paramètres ROI (Region of Interest)
  static const int roi_radius_pixels = 8;
  static const double confidence_threshold = 0.7;
  static const int min_valid_pixels = 10;
  
  // Paramètres de vitesse
  static const double max_speed_kmh = 200.0;
  static const double min_speed_kmh = 0.0;
  static const double speed_conversion_factor = 3.6; // m/s vers km/h
  
  // Paramètres EMA (Exponential Moving Average)
  static const int ema_window_ms = 250;
  
  // Paramètres par défaut utilisateur
  static const double default_speed_limit_kmh = 50.0;
  static const double default_tolerance_kmh = 5.0;
  static const bool default_auto_save = true;
  
  // Paramètres de capture photo
  static const int photo_capture_frames = 3; // ±1, ±2 frames
  static const double bbox_margin_percent = 0.15; // 15% de marge
  
  // Timeouts
  static const int vehicle_timeout_ms = 5000;
  static const int modal_auto_close_ms = 10000;
  
  // Chemins de fichiers
  static const String export_folder = 'radar_exports';
  static const String photos_folder = 'vehicle_photos';
  static const String csv_filename = 'speed_measurements.csv';
}

/// États possibles d'un véhicule dans le système
enum VehicleState {
  beforeA,    // Avant le portique A
  betweenAB,  // Entre les portiques A et B
  done        // Mesure terminée
}

/// Types d'événements du système
enum RadarEvent {
  vehicleDetected,
  vehicleLost,
  portiqueACrossed,
  portiqueBCrossed,
  speedCalculated,
  overspeedDetected,
  photoCapture,
  ocrCompleted
}
