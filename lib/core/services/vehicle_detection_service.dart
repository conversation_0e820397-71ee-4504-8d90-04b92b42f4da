import 'dart:async';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/services.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';
import '../models/vehicle_tracker.dart';

/// Service de détection et tracking des véhicules
class VehicleDetectionService {
  static const MethodChannel _channel = MethodChannel('radar_speed_detector/detection');
  
  final Map<String, VehicleTracker> _trackers = {};
  final StreamController<List<VehicleData>> _detectionStreamController = 
      StreamController<List<VehicleData>>.broadcast();
  
  bool _isInitialized = false;
  int _nextVehicleId = 1;
  
  Stream<List<VehicleData>> get detectionStream => _detectionStreamController.stream;
  
  /// Initialise le service de détection
  Future<void> initialize() async {
    try {
      _channel.setMethodCallHandler(_handleNativeCall);
      await _channel.invokeMethod('initializeDetection');
      _isInitialized = true;
    } catch (e) {
      throw Exception('Erreur lors de l\'initialisation de la détection: $e');
    }
  }
  
  /// Gère les appels depuis le code natif
  Future<void> _handleNativeCall(MethodCall call) async {
    switch (call.method) {
      case 'onVehiclesDetected':
        _handleDetectionResults(call.arguments);
        break;
      default:
        break;
    }
  }
  
  /// Traite les résultats de détection depuis le code natif
  void _handleDetectionResults(List<dynamic> detections) {
    final List<VehicleData> vehicles = [];
    final DateTime timestamp = DateTime.now();
    
    // Convertir les détections natives en objets VehicleData
    for (final detection in detections) {
      final bbox = BoundingBox(
        x: detection['x'].toDouble(),
        y: detection['y'].toDouble(),
        width: detection['width'].toDouble(),
        height: detection['height'].toDouble(),
      );
      
      final confidence = detection['confidence'].toDouble();
      
      // Créer ou mettre à jour le véhicule
      final vehicle = VehicleData(
        id: _getOrCreateVehicleId(bbox, confidence),
        bbox: bbox,
        confidence: confidence,
        timestamp: timestamp,
      );
      
      vehicles.add(vehicle);
    }
    
    // Mettre à jour les trackers
    _updateTrackers(vehicles);
    
    // Nettoyer les anciens trackers
    _cleanupOldTrackers();
    
    // Émettre les véhicules détectés
    _detectionStreamController.add(vehicles);
  }
  
  /// Détecte les véhicules dans une image (fallback Flutter)
  Future<List<VehicleData>> detectVehicles(CameraImage image) async {
    if (!_isInitialized) {
      throw Exception('Service de détection non initialisé');
    }
    
    try {
      // Envoyer l'image au code natif pour traitement
      final Uint8List imageBytes = _convertCameraImageToBytes(image);
      
      final result = await _channel.invokeMethod('detectVehicles', {
        'imageBytes': imageBytes,
        'width': image.width,
        'height': image.height,
        'format': image.format.group.name,
      });
      
      return _parseDetectionResults(result);
    } catch (e) {
      print('Erreur lors de la détection: $e');
      return [];
    }
  }
  
  /// Convertit une CameraImage en bytes
  Uint8List _convertCameraImageToBytes(CameraImage image) {
    // Implémentation simplifiée - à adapter selon le format d'image
    final WriteBuffer allBytes = WriteBuffer();
    for (final Plane plane in image.planes) {
      allBytes.putUint8List(plane.bytes);
    }
    return allBytes.done().buffer.asUint8List();
  }
  
  /// Parse les résultats de détection
  List<VehicleData> _parseDetectionResults(List<dynamic> results) {
    final List<VehicleData> vehicles = [];
    final DateTime timestamp = DateTime.now();
    
    for (final result in results) {
      final bbox = BoundingBox(
        x: result['x'].toDouble(),
        y: result['y'].toDouble(),
        width: result['width'].toDouble(),
        height: result['height'].toDouble(),
      );
      
      final vehicle = VehicleData(
        id: _getOrCreateVehicleId(bbox, result['confidence'].toDouble()),
        bbox: bbox,
        confidence: result['confidence'].toDouble(),
        timestamp: timestamp,
      );
      
      vehicles.add(vehicle);
    }
    
    return vehicles;
  }
  
  /// Obtient ou crée un ID de véhicule basé sur le tracking
  String _getOrCreateVehicleId(BoundingBox bbox, double confidence) {
    // Chercher un tracker existant proche
    VehicleTracker? bestMatch;
    double bestDistance = double.infinity;
    
    for (final tracker in _trackers.values) {
      final distance = _calculateDistance(bbox, tracker.lastBbox);
      if (distance < bestDistance && distance < 50.0) { // Seuil de 50 pixels
        bestDistance = distance;
        bestMatch = tracker;
      }
    }
    
    if (bestMatch != null) {
      // Mettre à jour le tracker existant
      bestMatch.update(bbox, confidence, DateTime.now());
      return bestMatch.id;
    } else {
      // Créer un nouveau tracker
      final id = 'vehicle_${_nextVehicleId++}';
      _trackers[id] = VehicleTracker(
        id: id,
        initialBbox: bbox,
        initialConfidence: confidence,
        timestamp: DateTime.now(),
      );
      return id;
    }
  }
  
  /// Calcule la distance entre deux bounding boxes
  double _calculateDistance(BoundingBox bbox1, BoundingBox bbox2) {
    final dx = bbox1.centerX - bbox2.centerX;
    final dy = bbox1.centerY - bbox2.centerY;
    return math.sqrt(dx * dx + dy * dy);
  }
  
  /// Met à jour les trackers avec les nouvelles détections
  void _updateTrackers(List<VehicleData> vehicles) {
    final Set<String> activeIds = vehicles.map((v) => v.id).toSet();
    
    // Marquer les trackers non vus comme perdus
    for (final tracker in _trackers.values) {
      if (!activeIds.contains(tracker.id)) {
        tracker.markAsLost();
      }
    }
  }
  
  /// Nettoie les anciens trackers
  void _cleanupOldTrackers() {
    final now = DateTime.now();
    final idsToRemove = <String>[];
    
    for (final entry in _trackers.entries) {
      final tracker = entry.value;
      final timeSinceLastSeen = now.difference(tracker.lastSeen).inMilliseconds;
      
      if (timeSinceLastSeen > AppConstants.vehicle_timeout_ms) {
        idsToRemove.add(entry.key);
      }
    }
    
    for (final id in idsToRemove) {
      _trackers.remove(id);
    }
  }
  
  /// Obtient tous les véhicules trackés
  List<VehicleData> getTrackedVehicles() {
    return _trackers.values.map((tracker) => tracker.toVehicleData()).toList();
  }
  
  /// Libère les ressources
  Future<void> dispose() async {
    await _detectionStreamController.close();
    _trackers.clear();
    _isInitialized = false;
  }
}

/// Classe pour tracker un véhicule individuel
class VehicleTracker {
  final String id;
  BoundingBox lastBbox;
  double lastConfidence;
  DateTime lastSeen;
  bool isLost;
  
  VehicleTracker({
    required this.id,
    required BoundingBox initialBbox,
    required double initialConfidence,
    required DateTime timestamp,
  }) : lastBbox = initialBbox,
       lastConfidence = initialConfidence,
       lastSeen = timestamp,
       isLost = false;
  
  void update(BoundingBox bbox, double confidence, DateTime timestamp) {
    lastBbox = bbox;
    lastConfidence = confidence;
    lastSeen = timestamp;
    isLost = false;
  }
  
  void markAsLost() {
    isLost = true;
  }
  
  VehicleData toVehicleData() {
    return VehicleData(
      id: id,
      bbox: lastBbox,
      confidence: lastConfidence,
      timestamp: lastSeen,
    );
  }
}
