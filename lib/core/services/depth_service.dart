import 'dart:math' as math;
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';

/// Service d'extraction et traitement des données de profondeur
class DepthService {
  
  /// Extrait la profondeur médiane d'une ROI (Region of Interest)
  /// Utilise le point bottom-center de la bounding box du véhicule
  double? extractMedianDepth(
    VehicleData vehicle,
    DepthData depthData,
  ) {
    try {
      final bbox = vehicle.bbox;
      final depthMap = depthData.depthMap;
      final confidenceMap = depthData.confidenceMap;
      
      // Calculer le point bottom-center
      final centerX = bbox.bottomCenterX.round();
      final centerY = bbox.bottomCenterY.round();
      
      // Vérifier les limites
      if (centerX < 0 || centerY < 0 || 
          centerY >= depthMap.length || 
          centerX >= depthMap[0].length) {
        return null;
      }
      
      // Extraire les valeurs de profondeur dans la ROI
      final List<double> validDepths = [];
      final int radius = AppConstants.roi_radius_pixels;
      
      for (int dy = -radius; dy <= radius; dy++) {
        for (int dx = -radius; dx <= radius; dx++) {
          final int x = centerX + dx;
          final int y = centerY + dy;
          
          // Vérifier les limites
          if (x >= 0 && x < depthMap[0].length && 
              y >= 0 && y < depthMap.length) {
            
            final double depth = depthMap[y][x];
            final double confidence = confidenceMap[y][x];
            
            // Filtrer par confiance et valeur valide
            if (confidence >= AppConstants.confidence_threshold && 
                depth > 0 && depth < 100) { // Limite raisonnable de 100m
              validDepths.add(depth);
            }
          }
        }
      }
      
      // Vérifier qu'on a assez de pixels valides
      if (validDepths.length < AppConstants.min_valid_pixels) {
        return null;
      }
      
      // Calculer la médiane
      validDepths.sort();
      final int middle = validDepths.length ~/ 2;
      
      if (validDepths.length % 2 == 0) {
        return (validDepths[middle - 1] + validDepths[middle]) / 2.0;
      } else {
        return validDepths[middle];
      }
      
    } catch (e) {
      print('Erreur lors de l\'extraction de profondeur: $e');
      return null;
    }
  }
  
  /// Détecte si un véhicule franchit un portique avec hystérésis
  bool detectPortiqueCrossing(
    List<DepthMeasurement> depthHistory,
    double portiqueDistance,
    double hysteresisThreshold,
  ) {
    if (depthHistory.length < 2) return false;
    
    final current = depthHistory.last;
    final previous = depthHistory[depthHistory.length - 2];
    
    // Vérifier le franchissement avec hystérésis
    final threshold = portiqueDistance - hysteresisThreshold;
    
    return previous.depth > threshold && current.depth <= threshold;
  }
  
  /// Calcule le temps de franchissement avec interpolation sous-trame
  DateTime? calculateSubframeCrossingTime(
    List<DepthMeasurement> depthHistory,
    double portiqueDistance,
  ) {
    if (depthHistory.length < 2) return null;
    
    // Trouver les deux points autour du franchissement
    DepthMeasurement? beforeCrossing;
    DepthMeasurement? afterCrossing;
    
    for (int i = depthHistory.length - 1; i >= 1; i--) {
      final current = depthHistory[i];
      final previous = depthHistory[i - 1];
      
      if (previous.depth > portiqueDistance && current.depth <= portiqueDistance) {
        beforeCrossing = previous;
        afterCrossing = current;
        break;
      }
    }
    
    if (beforeCrossing == null || afterCrossing == null) return null;
    
    // Interpolation linéaire pour trouver le temps exact de franchissement
    final double depthDiff = beforeCrossing.depth - afterCrossing.depth;
    if (depthDiff == 0) return afterCrossing.timestamp;
    
    final double ratio = (beforeCrossing.depth - portiqueDistance) / depthDiff;
    final int timeDiffMs = afterCrossing.timestamp.millisecondsSinceEpoch - 
                          beforeCrossing.timestamp.millisecondsSinceEpoch;
    
    final int interpolatedTimeMs = beforeCrossing.timestamp.millisecondsSinceEpoch + 
                                  (timeDiffMs * ratio).round();
    
    return DateTime.fromMillisecondsSinceEpoch(interpolatedTimeMs);
  }
  
  /// Calcule le temps de franchissement avec régression linéaire locale
  DateTime? calculateRegressionCrossingTime(
    List<DepthMeasurement> depthHistory,
    double portiqueDistance,
    {int windowSize = 5}
  ) {
    if (depthHistory.length < windowSize) return null;
    
    // Prendre les derniers points pour la régression
    final recentMeasurements = depthHistory.take(windowSize).toList();
    
    // Régression linéaire simple: y = ax + b où y = profondeur, x = temps
    double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
    final int n = recentMeasurements.length;
    
    for (int i = 0; i < n; i++) {
      final x = recentMeasurements[i].timestamp.millisecondsSinceEpoch.toDouble();
      final y = recentMeasurements[i].depth;
      
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumX2 += x * x;
    }
    
    final double denominator = n * sumX2 - sumX * sumX;
    if (denominator.abs() < 1e-10) return null; // Éviter division par zéro
    
    final double a = (n * sumXY - sumX * sumY) / denominator;
    final double b = (sumY - a * sumX) / n;
    
    // Résoudre pour trouver le temps où profondeur = portiqueDistance
    // portiqueDistance = a * t + b
    // t = (portiqueDistance - b) / a
    if (a.abs() < 1e-10) return null; // Pas de changement de profondeur
    
    final double crossingTimeMs = (portiqueDistance - b) / a;
    return DateTime.fromMillisecondsSinceEpoch(crossingTimeMs.round());
  }
  
  /// Filtre les mesures de profondeur aberrantes
  List<DepthMeasurement> filterOutliers(
    List<DepthMeasurement> measurements,
    {double maxDeviationFactor = 2.0}
  ) {
    if (measurements.length < 3) return measurements;
    
    // Calculer la médiane et l'écart médian absolu
    final depths = measurements.map((m) => m.depth).toList()..sort();
    final median = depths[depths.length ~/ 2];
    
    final deviations = depths.map((d) => (d - median).abs()).toList()..sort();
    final mad = deviations[deviations.length ~/ 2];
    
    // Filtrer les valeurs aberrantes
    final threshold = mad * maxDeviationFactor;
    
    return measurements.where((m) {
      return (m.depth - median).abs() <= threshold;
    }).toList();
  }
  
  /// Lisse les mesures de profondeur avec un filtre passe-bas
  List<DepthMeasurement> smoothDepthMeasurements(
    List<DepthMeasurement> measurements,
    {double alpha = 0.3}
  ) {
    if (measurements.isEmpty) return measurements;
    
    final List<DepthMeasurement> smoothed = [measurements.first];
    
    for (int i = 1; i < measurements.length; i++) {
      final current = measurements[i];
      final previousSmoothed = smoothed.last.depth;
      
      final smoothedDepth = alpha * current.depth + (1 - alpha) * previousSmoothed;
      
      smoothed.add(DepthMeasurement(
        depth: smoothedDepth,
        timestamp: current.timestamp,
      ));
    }
    
    return smoothed;
  }
}

/// Classe pour stocker une mesure de profondeur avec timestamp
class DepthMeasurement {
  final double depth;
  final DateTime timestamp;
  
  const DepthMeasurement({
    required this.depth,
    required this.timestamp,
  });
}
