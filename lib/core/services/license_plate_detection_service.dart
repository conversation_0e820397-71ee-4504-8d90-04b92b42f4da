import 'dart:async';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import '../models/vehicle_data.dart';
import 'ocr_service.dart';

/// Service de détection et reconnaissance de plaques d'immatriculation
class LicensePlateDetectionService {
  final OCRService _ocrService = OCRService();
  
  // État du service
  bool _isInitialized = false;
  bool _isEnabled = false;
  
  // Cache des plaques détectées pour éviter les re-calculs
  final Map<String, LicensePlateResult> _plateCache = {};
  
  // Stream des résultats de détection de plaques
  final StreamController<LicensePlateResult> _plateStreamController = 
      StreamController<LicensePlateResult>.broadcast();
  
  // Getters
  Stream<LicensePlateResult> get plateStream => _plateStreamController.stream;
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled;
  
  /// Initialise le service de détection de plaques
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _ocrService.initialize();
      _isInitialized = true;
      print('Service de détection de plaques initialisé');
    } catch (e) {
      throw Exception('Erreur lors de l\'initialisation du service de plaques: $e');
    }
  }
  
  /// Active ou désactive la détection de plaques
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled) {
      _plateCache.clear();
    }
  }
  
  /// Détecte et lit la plaque d'un véhicule
  Future<String?> detectLicensePlate(
    VehicleData vehicle, 
    CameraImage cameraImage
  ) async {
    if (!_isInitialized || !_isEnabled) return null;
    
    // Vérifier le cache d'abord
    final cachedResult = _plateCache[vehicle.id];
    if (cachedResult != null && 
        cachedResult.confidence > 0.7 && 
        DateTime.now().difference(cachedResult.timestamp).inSeconds < 5) {
      return cachedResult.plateText;
    }
    
    try {
      // Extraire la région de la plaque depuis la bounding box du véhicule
      final plateRegion = _extractPlateRegion(vehicle.bbox, cameraImage);
      
      if (plateRegion == null) return null;
      
      // Effectuer l'OCR sur la région de la plaque
      final plateText = await _ocrService.recognizeLicensePlate(
        cameraImage,
        x: plateRegion.x,
        y: plateRegion.y,
        width: plateRegion.width,
        height: plateRegion.height,
      );
      
      if (plateText != null && plateText.isNotEmpty) {
        // Valider et formater la plaque
        final formattedPlate = _ocrService.formatLicensePlate(plateText);
        
        // Calculer la confiance basée sur la qualité de la détection
        final confidence = _calculatePlateConfidence(plateText, vehicle);
        
        // Mettre en cache le résultat
        final result = LicensePlateResult(
          vehicleId: vehicle.id,
          plateText: formattedPlate,
          confidence: confidence,
          timestamp: DateTime.now(),
          region: plateRegion,
        );
        
        _plateCache[vehicle.id] = result;
        
        // Émettre le résultat
        _plateStreamController.add(result);
        
        return formattedPlate;
      }
    } catch (e) {
      print('Erreur lors de la détection de plaque pour ${vehicle.id}: $e');
    }
    
    return null;
  }
  
  /// Extrait la région probable de la plaque depuis la bounding box du véhicule
  PlateRegion? _extractPlateRegion(BoundingBox vehicleBbox, CameraImage image) {
    // Les plaques sont généralement situées dans la partie inférieure du véhicule
    // et occupent environ 15-25% de la largeur et 8-15% de la hauteur
    
    final imageWidth = image.width.toDouble();
    final imageHeight = image.height.toDouble();
    
    // Convertir les coordonnées normalisées en pixels
    final vehicleLeft = vehicleBbox.x * imageWidth;
    final vehicleTop = vehicleBbox.y * imageHeight;
    final vehicleWidth = vehicleBbox.width * imageWidth;
    final vehicleHeight = vehicleBbox.height * imageHeight;
    
    // Région de recherche de plaque (partie inférieure du véhicule)
    final plateSearchTop = vehicleTop + vehicleHeight * 0.6; // 60% vers le bas
    final plateSearchHeight = vehicleHeight * 0.3; // 30% de la hauteur du véhicule
    
    // Largeur de recherche (centrée)
    final plateSearchWidth = vehicleWidth * 0.8; // 80% de la largeur du véhicule
    final plateSearchLeft = vehicleLeft + (vehicleWidth - plateSearchWidth) / 2;
    
    // Vérifier que la région est dans les limites de l'image
    if (plateSearchLeft < 0 || plateSearchTop < 0 ||
        plateSearchLeft + plateSearchWidth > imageWidth ||
        plateSearchTop + plateSearchHeight > imageHeight) {
      return null;
    }
    
    return PlateRegion(
      x: plateSearchLeft,
      y: plateSearchTop,
      width: plateSearchWidth,
      height: plateSearchHeight,
    );
  }
  
  /// Calcule la confiance de la détection de plaque
  double _calculatePlateConfidence(String plateText, VehicleData vehicle) {
    double confidence = 0.5; // Confiance de base
    
    // Bonus pour la longueur appropriée
    if (plateText.length >= 6 && plateText.length <= 9) {
      confidence += 0.2;
    }
    
    // Bonus pour les caractères alphanumériques
    final alphanumericCount = plateText.replaceAll(RegExp(r'[^A-Z0-9]'), '').length;
    confidence += (alphanumericCount / plateText.length) * 0.2;
    
    // Bonus basé sur la confiance du véhicule
    confidence += vehicle.confidence * 0.1;
    
    // Malus si le véhicule est trop petit (plaque probablement illisible)
    final vehicleSize = vehicle.bbox.width * vehicle.bbox.height;
    if (vehicleSize < 0.01) { // Moins de 1% de l'écran
      confidence -= 0.3;
    }
    
    return confidence.clamp(0.0, 1.0);
  }
  
  /// Obtient le résultat de plaque en cache pour un véhicule
  LicensePlateResult? getCachedPlateResult(String vehicleId) {
    return _plateCache[vehicleId];
  }
  
  /// Nettoie le cache des plaques anciennes
  void cleanupCache() {
    final now = DateTime.now();
    final idsToRemove = <String>[];
    
    for (final entry in _plateCache.entries) {
      if (now.difference(entry.value.timestamp).inSeconds > 30) {
        idsToRemove.add(entry.key);
      }
    }
    
    for (final id in idsToRemove) {
      _plateCache.remove(id);
    }
  }
  
  /// Traite plusieurs véhicules en batch pour optimiser les performances
  Future<Map<String, String>> detectMultiplePlates(
    List<VehicleData> vehicles, 
    CameraImage cameraImage
  ) async {
    if (!_isInitialized || !_isEnabled) return {};
    
    final results = <String, String>{};
    
    // Traiter seulement les véhicules les plus prometteurs (taille suffisante, confiance élevée)
    final candidateVehicles = vehicles
        .where((v) => v.confidence > 0.6 && 
                     v.bbox.width * v.bbox.height > 0.005) // Au moins 0.5% de l'écran
        .take(3) // Limiter à 3 véhicules par frame pour les performances
        .toList();
    
    for (final vehicle in candidateVehicles) {
      final plateText = await detectLicensePlate(vehicle, cameraImage);
      if (plateText != null) {
        results[vehicle.id] = plateText;
      }
    }
    
    return results;
  }
  
  /// Libère les ressources
  Future<void> dispose() async {
    await _ocrService.dispose();
    await _plateStreamController.close();
    _plateCache.clear();
    _isInitialized = false;
  }
}

/// Région de recherche de plaque
class PlateRegion {
  final double x;
  final double y;
  final double width;
  final double height;
  
  const PlateRegion({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });
}

/// Résultat de détection de plaque
class LicensePlateResult {
  final String vehicleId;
  final String plateText;
  final double confidence;
  final DateTime timestamp;
  final PlateRegion region;
  
  const LicensePlateResult({
    required this.vehicleId,
    required this.plateText,
    required this.confidence,
    required this.timestamp,
    required this.region,
  });
}
