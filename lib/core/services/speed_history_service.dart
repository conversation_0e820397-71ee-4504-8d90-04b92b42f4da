import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';

/// Service de gestion de l'historique des vitesses et statistiques
class SpeedHistoryService {
  // Historique des mesures de vitesse
  final List<SpeedMeasurement> _speedHistory = [];
  
  // Statistiques en temps réel
  SpeedStatistics _currentStats = SpeedStatistics();
  
  // Stream des statistiques
  final StreamController<SpeedStatistics> _statsController = 
      StreamController<SpeedStatistics>.broadcast();
  
  // Préférences partagées pour la persistance
  SharedPreferences? _prefs;
  
  // Getters
  Stream<SpeedStatistics> get statisticsStream => _statsController.stream;
  SpeedStatistics get currentStatistics => _currentStats;
  List<SpeedMeasurement> get speedHistory => List.unmodifiable(_speedHistory);
  
  /// Initialise le service d'historique
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadHistoryFromStorage();
    _updateStatistics();
  }
  
  /// Ajoute une nouvelle mesure de vitesse
  void addSpeedMeasurement(VehicleData vehicle, double speedKmh) {
    final measurement = SpeedMeasurement(
      vehicleId: vehicle.id,
      speed: speedKmh,
      timestamp: DateTime.now(),
      licensePlate: vehicle.licensePlate,
      driverInfo: vehicle.driverInfo,
      confidence: vehicle.confidence,
      location: null, // TODO: Ajouter la géolocalisation si nécessaire
    );
    
    _speedHistory.add(measurement);
    
    // Limiter la taille de l'historique en mémoire (garder les 1000 dernières)
    if (_speedHistory.length > 1000) {
      _speedHistory.removeAt(0);
    }
    
    // Mettre à jour les statistiques
    _updateStatistics();
    
    // Sauvegarder périodiquement
    _saveHistoryToStorage();
  }
  
  /// Met à jour les statistiques en temps réel
  void _updateStatistics() {
    if (_speedHistory.isEmpty) {
      _currentStats = SpeedStatistics();
      _statsController.add(_currentStats);
      return;
    }
    
    // Calculer les statistiques de base
    final speeds = _speedHistory.map((m) => m.speed).toList();
    speeds.sort();
    
    final totalMeasurements = speeds.length;
    final averageSpeed = speeds.reduce((a, b) => a + b) / totalMeasurements;
    final maxSpeed = speeds.last;
    final minSpeed = speeds.first;
    final medianSpeed = speeds[speeds.length ~/ 2];
    
    // Statistiques par période
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisWeek = now.subtract(const Duration(days: 7));
    final thisMonth = DateTime(now.year, now.month, 1);
    
    final todayMeasurements = _speedHistory
        .where((m) => m.timestamp.isAfter(today))
        .toList();
    
    final weekMeasurements = _speedHistory
        .where((m) => m.timestamp.isAfter(thisWeek))
        .toList();
    
    final monthMeasurements = _speedHistory
        .where((m) => m.timestamp.isAfter(thisMonth))
        .toList();
    
    // Calculer les dépassements
    final speedLimit = 50.0; // TODO: Utiliser la limite configurée
    final overspeeds = _speedHistory
        .where((m) => m.speed > speedLimit)
        .toList();
    
    // Distribution des vitesses
    final speedRanges = _calculateSpeedDistribution(speeds);
    
    _currentStats = SpeedStatistics(
      totalMeasurements: totalMeasurements,
      averageSpeed: averageSpeed,
      maxSpeed: maxSpeed,
      minSpeed: minSpeed,
      medianSpeed: medianSpeed,
      todayCount: todayMeasurements.length,
      weekCount: weekMeasurements.length,
      monthCount: monthMeasurements.length,
      overspeedCount: overspeeds.length,
      overspeedPercentage: (overspeeds.length / totalMeasurements) * 100,
      speedDistribution: speedRanges,
      lastUpdated: now,
    );
    
    _statsController.add(_currentStats);
  }
  
  /// Calcule la distribution des vitesses par tranches
  Map<String, int> _calculateSpeedDistribution(List<double> speeds) {
    final distribution = <String, int>{
      '0-30': 0,
      '31-50': 0,
      '51-70': 0,
      '71-90': 0,
      '91-110': 0,
      '110+': 0,
    };
    
    for (final speed in speeds) {
      if (speed <= 30) {
        distribution['0-30'] = distribution['0-30']! + 1;
      } else if (speed <= 50) {
        distribution['31-50'] = distribution['31-50']! + 1;
      } else if (speed <= 70) {
        distribution['51-70'] = distribution['51-70']! + 1;
      } else if (speed <= 90) {
        distribution['71-90'] = distribution['71-90']! + 1;
      } else if (speed <= 110) {
        distribution['91-110'] = distribution['91-110']! + 1;
      } else {
        distribution['110+'] = distribution['110+']! + 1;
      }
    }
    
    return distribution;
  }
  
  /// Obtient les mesures d'une période spécifique
  List<SpeedMeasurement> getMeasurementsForPeriod(DateTime start, DateTime end) {
    return _speedHistory
        .where((m) => m.timestamp.isAfter(start) && m.timestamp.isBefore(end))
        .toList();
  }
  
  /// Obtient les dépassements de vitesse
  List<SpeedMeasurement> getOverspeeds(double speedLimit) {
    return _speedHistory
        .where((m) => m.speed > speedLimit)
        .toList();
  }
  
  /// Exporte l'historique au format CSV
  Future<String> exportToCSV() async {
    final buffer = StringBuffer();
    
    // En-têtes CSV
    buffer.writeln('Timestamp,Vehicle ID,Speed (km/h),License Plate,Driver Info,Confidence');
    
    // Données
    for (final measurement in _speedHistory) {
      buffer.writeln([
        measurement.timestamp.toIso8601String(),
        measurement.vehicleId,
        measurement.speed.toStringAsFixed(1),
        measurement.licensePlate ?? '',
        measurement.driverInfo ?? '',
        measurement.confidence.toStringAsFixed(2),
      ].join(','));
    }
    
    // Sauvegarder dans un fichier
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/${AppConstants.csv_filename}');
    await file.writeAsString(buffer.toString());
    
    return file.path;
  }
  
  /// Sauvegarde l'historique dans le stockage local
  Future<void> _saveHistoryToStorage() async {
    if (_prefs == null) return;
    
    try {
      // Sauvegarder seulement les 500 dernières mesures pour éviter les problèmes de taille
      final recentHistory = _speedHistory.length > 500 
          ? _speedHistory.sublist(_speedHistory.length - 500)
          : _speedHistory;
      
      final jsonData = recentHistory
          .map((m) => m.toJson())
          .toList();
      
      await _prefs!.setString('speed_history', jsonEncode(jsonData));
    } catch (e) {
      print('Erreur lors de la sauvegarde de l\'historique: $e');
    }
  }
  
  /// Charge l'historique depuis le stockage local
  Future<void> _loadHistoryFromStorage() async {
    if (_prefs == null) return;
    
    try {
      final jsonString = _prefs!.getString('speed_history');
      if (jsonString != null) {
        final jsonData = jsonDecode(jsonString) as List;
        _speedHistory.clear();
        _speedHistory.addAll(
          jsonData.map((json) => SpeedMeasurement.fromJson(json))
        );
      }
    } catch (e) {
      print('Erreur lors du chargement de l\'historique: $e');
    }
  }
  
  /// Efface tout l'historique
  Future<void> clearHistory() async {
    _speedHistory.clear();
    _updateStatistics();
    
    if (_prefs != null) {
      await _prefs!.remove('speed_history');
    }
  }
  
  /// Libère les ressources
  void dispose() {
    _statsController.close();
    _speedHistory.clear();
  }
}

/// Mesure de vitesse individuelle
class SpeedMeasurement {
  final String vehicleId;
  final double speed;
  final DateTime timestamp;
  final String? licensePlate;
  final String? driverInfo;
  final double confidence;
  final Location? location;
  
  const SpeedMeasurement({
    required this.vehicleId,
    required this.speed,
    required this.timestamp,
    this.licensePlate,
    this.driverInfo,
    required this.confidence,
    this.location,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'vehicleId': vehicleId,
      'speed': speed,
      'timestamp': timestamp.toIso8601String(),
      'licensePlate': licensePlate,
      'driverInfo': driverInfo,
      'confidence': confidence,
      'location': location?.toJson(),
    };
  }
  
  factory SpeedMeasurement.fromJson(Map<String, dynamic> json) {
    return SpeedMeasurement(
      vehicleId: json['vehicleId'],
      speed: json['speed'].toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      licensePlate: json['licensePlate'],
      driverInfo: json['driverInfo'],
      confidence: json['confidence'].toDouble(),
      location: json['location'] != null 
          ? Location.fromJson(json['location'])
          : null,
    );
  }
}

/// Statistiques de vitesse
class SpeedStatistics {
  final int totalMeasurements;
  final double averageSpeed;
  final double maxSpeed;
  final double minSpeed;
  final double medianSpeed;
  final int todayCount;
  final int weekCount;
  final int monthCount;
  final int overspeedCount;
  final double overspeedPercentage;
  final Map<String, int> speedDistribution;
  final DateTime lastUpdated;

  SpeedStatistics({
    this.totalMeasurements = 0,
    this.averageSpeed = 0.0,
    this.maxSpeed = 0.0,
    this.minSpeed = 0.0,
    this.medianSpeed = 0.0,
    this.todayCount = 0,
    this.weekCount = 0,
    this.monthCount = 0,
    this.overspeedCount = 0,
    this.overspeedPercentage = 0.0,
    this.speedDistribution = const {},
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();
}

/// Classe Location simple
class Location {
  final double latitude;
  final double longitude;

  const Location({
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
    );
  }
}
