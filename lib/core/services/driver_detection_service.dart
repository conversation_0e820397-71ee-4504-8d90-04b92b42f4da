import 'dart:async';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import '../models/vehicle_data.dart';

/// Service de détection de conducteurs et passagers dans les véhicules
class DriverDetectionService {
  // ML Kit détecteurs
  late FaceDetector _faceDetector;
  late PoseDetector _poseDetector;
  
  // État du service
  bool _isInitialized = false;
  bool _isEnabled = false;
  
  // Cache des détections pour éviter les re-calculs
  final Map<String, DriverDetectionResult> _detectionCache = {};
  
  // Stream des résultats de détection
  final StreamController<DriverDetectionResult> _detectionStreamController = 
      StreamController<DriverDetectionResult>.broadcast();
  
  // Getters
  Stream<DriverDetectionResult> get detectionStream => _detectionStreamController.stream;
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled;
  
  /// Initialise le service de détection de conducteurs
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Configurer le détecteur de visages
      final faceDetectorOptions = FaceDetectorOptions(
        enableContours: false,
        enableLandmarks: false,
        enableClassification: false,
        enableTracking: true,
        minFaceSize: 0.1, // Visages d'au moins 10% de l'image
        performanceMode: FaceDetectorMode.fast,
      );
      _faceDetector = FaceDetector(options: faceDetectorOptions);
      
      // Configurer le détecteur de poses
      final poseDetectorOptions = PoseDetectorOptions(
        mode: PoseDetectionMode.stream,
        model: PoseDetectionModel.base,
      );
      _poseDetector = PoseDetector(options: poseDetectorOptions);
      
      _isInitialized = true;
      print('Service de détection de conducteurs initialisé');
    } catch (e) {
      throw Exception('Erreur lors de l\'initialisation du service de conducteurs: $e');
    }
  }
  
  /// Active ou désactive la détection de conducteurs
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled) {
      _detectionCache.clear();
    }
  }
  
  /// Détecte les conducteurs et passagers dans un véhicule
  Future<String?> detectDrivers(VehicleData vehicle, CameraImage cameraImage) async {
    if (!_isInitialized || !_isEnabled) return null;
    
    // Vérifier le cache d'abord
    final cachedResult = _detectionCache[vehicle.id];
    if (cachedResult != null && 
        DateTime.now().difference(cachedResult.timestamp).inSeconds < 3) {
      return cachedResult.description;
    }
    
    try {
      // Convertir CameraImage en InputImage
      final inputImage = _convertCameraImageToInputImage(cameraImage);
      if (inputImage == null) return null;
      
      // Extraire la région du véhicule pour la détection
      final vehicleRegion = _extractVehicleInterior(vehicle.bbox, cameraImage);
      
      // Détecter les visages dans la région du véhicule
      final faces = await _faceDetector.processImage(inputImage);
      final vehicleFaces = _filterFacesInVehicle(faces, vehicleRegion, cameraImage);
      
      // Détecter les poses dans la région du véhicule
      final poses = await _poseDetector.processImage(inputImage);
      final vehiclePoses = _filterPosesInVehicle(poses, vehicleRegion, cameraImage);
      
      // Analyser les résultats
      final result = _analyzeDetections(vehicle.id, vehicleFaces, vehiclePoses);
      
      if (result != null) {
        _detectionCache[vehicle.id] = result;
        _detectionStreamController.add(result);
        return result.description;
      }
      
    } catch (e) {
      print('Erreur lors de la détection de conducteur pour ${vehicle.id}: $e');
    }
    
    return null;
  }
  
  /// Convertit CameraImage en InputImage pour ML Kit
  InputImage? _convertCameraImageToInputImage(CameraImage cameraImage) {
    try {
      final allBytes = <int>[];
      for (final Plane plane in cameraImage.planes) {
        allBytes.addAll(plane.bytes);
      }
      final bytes = Uint8List.fromList(allBytes);

      final Size imageSize = Size(
        cameraImage.width.toDouble(),
        cameraImage.height.toDouble(),
      );

      const InputImageRotation imageRotation = InputImageRotation.rotation0deg;
      const InputImageFormat inputImageFormat = InputImageFormat.nv21;

      final inputImageData = InputImageMetadata(
        size: imageSize,
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: cameraImage.planes[0].bytesPerRow,
      );

      return InputImage.fromBytes(
        bytes: bytes,
        metadata: inputImageData,
      );
    } catch (e) {
      print('Erreur lors de la conversion CameraImage: $e');
      return null;
    }
  }
  
  /// Extrait la région intérieure du véhicule (habitacle)
  VehicleInteriorRegion _extractVehicleInterior(BoundingBox vehicleBbox, CameraImage image) {
    final imageWidth = image.width.toDouble();
    final imageHeight = image.height.toDouble();
    
    // Convertir les coordonnées normalisées en pixels
    final vehicleLeft = vehicleBbox.x * imageWidth;
    final vehicleTop = vehicleBbox.y * imageHeight;
    final vehicleWidth = vehicleBbox.width * imageWidth;
    final vehicleHeight = vehicleBbox.height * imageHeight;
    
    // L'habitacle est généralement dans la partie supérieure du véhicule
    // et occupe environ 60-80% de la largeur et 40-60% de la hauteur
    final interiorTop = vehicleTop + vehicleHeight * 0.1; // 10% du haut
    final interiorHeight = vehicleHeight * 0.5; // 50% de la hauteur
    final interiorLeft = vehicleLeft + vehicleWidth * 0.1; // 10% de la gauche
    final interiorWidth = vehicleWidth * 0.8; // 80% de la largeur
    
    return VehicleInteriorRegion(
      left: interiorLeft,
      top: interiorTop,
      width: interiorWidth,
      height: interiorHeight,
    );
  }
  
  /// Filtre les visages qui sont dans la région du véhicule
  List<Face> _filterFacesInVehicle(
    List<Face> faces, 
    VehicleInteriorRegion vehicleRegion, 
    CameraImage image
  ) {
    return faces.where((face) {
      final faceRect = face.boundingBox;
      final faceCenterX = faceRect.left + faceRect.width / 2;
      final faceCenterY = faceRect.top + faceRect.height / 2;
      
      return faceCenterX >= vehicleRegion.left &&
             faceCenterX <= vehicleRegion.left + vehicleRegion.width &&
             faceCenterY >= vehicleRegion.top &&
             faceCenterY <= vehicleRegion.top + vehicleRegion.height;
    }).toList();
  }
  
  /// Filtre les poses qui sont dans la région du véhicule
  List<Pose> _filterPosesInVehicle(
    List<Pose> poses, 
    VehicleInteriorRegion vehicleRegion, 
    CameraImage image
  ) {
    return poses.where((pose) {
      // Utiliser la position de la tête/nez pour déterminer si la pose est dans le véhicule
      final noseLandmark = pose.landmarks[PoseLandmarkType.nose];
      if (noseLandmark == null) return false;
      
      final noseX = noseLandmark.x;
      final noseY = noseLandmark.y;
      
      return noseX >= vehicleRegion.left &&
             noseX <= vehicleRegion.left + vehicleRegion.width &&
             noseY >= vehicleRegion.top &&
             noseY <= vehicleRegion.top + vehicleRegion.height;
    }).toList();
  }
  
  /// Analyse les détections et génère un résultat
  DriverDetectionResult? _analyzeDetections(
    String vehicleId, 
    List<Face> faces, 
    List<Pose> poses
  ) {
    if (faces.isEmpty && poses.isEmpty) return null;
    
    final faceCount = faces.length;
    final poseCount = poses.length;
    final totalDetections = math.max(faceCount, poseCount);
    
    String description;
    double confidence = 0.0;
    
    if (totalDetections == 1) {
      description = "1 personne";
      confidence = 0.8;
    } else if (totalDetections == 2) {
      description = "2 personnes";
      confidence = 0.7;
    } else if (totalDetections > 2) {
      description = "$totalDetections personnes";
      confidence = 0.6;
    } else {
      return null;
    }
    
    // Ajuster la confiance basée sur la cohérence entre visages et poses
    if (faceCount > 0 && poseCount > 0) {
      final coherence = 1.0 - (faceCount - poseCount).abs() / math.max(faceCount, poseCount);
      confidence *= coherence;
    }
    
    return DriverDetectionResult(
      vehicleId: vehicleId,
      description: description,
      faceCount: faceCount,
      poseCount: poseCount,
      confidence: confidence,
      timestamp: DateTime.now(),
    );
  }
  
  /// Obtient le résultat de détection en cache pour un véhicule
  DriverDetectionResult? getCachedDetectionResult(String vehicleId) {
    return _detectionCache[vehicleId];
  }
  
  /// Nettoie le cache des détections anciennes
  void cleanupCache() {
    final now = DateTime.now();
    final idsToRemove = <String>[];
    
    for (final entry in _detectionCache.entries) {
      if (now.difference(entry.value.timestamp).inSeconds > 10) {
        idsToRemove.add(entry.key);
      }
    }
    
    for (final id in idsToRemove) {
      _detectionCache.remove(id);
    }
  }
  
  /// Traite plusieurs véhicules en batch
  Future<Map<String, String>> detectMultipleDrivers(
    List<VehicleData> vehicles, 
    CameraImage cameraImage
  ) async {
    if (!_isInitialized || !_isEnabled) return {};
    
    final results = <String, String>{};
    
    // Traiter seulement les véhicules les plus prometteurs
    final candidateVehicles = vehicles
        .where((v) => v.confidence > 0.7 && 
                     v.bbox.width * v.bbox.height > 0.01) // Au moins 1% de l'écran
        .take(2) // Limiter à 2 véhicules par frame pour les performances
        .toList();
    
    for (final vehicle in candidateVehicles) {
      final driverInfo = await detectDrivers(vehicle, cameraImage);
      if (driverInfo != null) {
        results[vehicle.id] = driverInfo;
      }
    }
    
    return results;
  }
  
  /// Libère les ressources
  Future<void> dispose() async {
    await _faceDetector.close();
    await _poseDetector.close();
    await _detectionStreamController.close();
    _detectionCache.clear();
    _isInitialized = false;
  }
}

/// Région intérieure d'un véhicule
class VehicleInteriorRegion {
  final double left;
  final double top;
  final double width;
  final double height;
  
  const VehicleInteriorRegion({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });
}

/// Résultat de détection de conducteur
class DriverDetectionResult {
  final String vehicleId;
  final String description;
  final int faceCount;
  final int poseCount;
  final double confidence;
  final DateTime timestamp;
  
  const DriverDetectionResult({
    required this.vehicleId,
    required this.description,
    required this.faceCount,
    required this.poseCount,
    required this.confidence,
    required this.timestamp,
  });
}
