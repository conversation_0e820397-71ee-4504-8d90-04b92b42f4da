import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service de gestion des langues
class LanguageService {
  static const String _languageKey = 'selected_language';
  
  /// Langues supportées
  static const List<LanguageOption> supportedLanguages = [
    LanguageOption(
      code: 'en',
      name: 'English',
      nativeName: 'English',
      locale: Locale('en'),
      flag: '🇺🇸',
    ),
    LanguageOption(
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      locale: Locale('fr'),
      flag: '🇫🇷',
    ),
    LanguageOption(
      code: 'es',
      name: 'Spanish',
      nativeName: 'Español',
      locale: Locale('es'),
      flag: '🇪🇸',
    ),
    LanguageOption(
      code: 'de',
      name: 'German',
      nativeName: 'Deutsch',
      locale: Locale('de'),
      flag: '🇩🇪',
    ),
    LanguageOption(
      code: 'it',
      name: 'Italian',
      nativeName: 'Italiano',
      locale: Locale('it'),
      flag: '🇮🇹',
    ),
    LanguageOption(
      code: 'pt',
      name: 'Portuguese',
      nativeName: 'Português',
      locale: Locale('pt'),
      flag: '🇵🇹',
    ),
    LanguageOption(
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      locale: Locale('ar'),
      flag: '🇸🇦',
    ),
    LanguageOption(
      code: 'zh',
      name: 'Chinese',
      nativeName: '中文',
      locale: Locale('zh'),
      flag: '🇨🇳',
    ),
    LanguageOption(
      code: 'ja',
      name: 'Japanese',
      nativeName: '日本語',
      locale: Locale('ja'),
      flag: '🇯🇵',
    ),
    LanguageOption(
      code: 'ru',
      name: 'Russian',
      nativeName: 'Русский',
      locale: Locale('ru'),
      flag: '🇷🇺',
    ),
  ];
  
  /// Obtient la langue sauvegardée ou la langue par défaut
  static Future<Locale> getSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey);
      
      if (languageCode != null) {
        final language = supportedLanguages.firstWhere(
          (lang) => lang.code == languageCode,
          orElse: () => supportedLanguages.first,
        );
        return language.locale;
      }
      
      // Utiliser la langue du système si supportée
      final systemLocale = PlatformDispatcher.instance.locale;
      final systemLanguage = supportedLanguages.where(
        (lang) => lang.locale.languageCode == systemLocale.languageCode,
      ).firstOrNull;
      
      if (systemLanguage != null) {
        return systemLanguage.locale;
      }
      
      // Par défaut: anglais
      return supportedLanguages.first.locale;
    } catch (e) {
      return supportedLanguages.first.locale;
    }
  }
  
  /// Sauvegarde la langue sélectionnée
  static Future<void> saveLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      print('Erreur lors de la sauvegarde de la langue: $e');
    }
  }
  
  /// Obtient une option de langue par son code
  static LanguageOption? getLanguageByCode(String code) {
    try {
      return supportedLanguages.firstWhere((lang) => lang.code == code);
    } catch (e) {
      return null;
    }
  }
  
  /// Obtient une option de langue par sa locale
  static LanguageOption? getLanguageByLocale(Locale locale) {
    try {
      return supportedLanguages.firstWhere(
        (lang) => lang.locale.languageCode == locale.languageCode,
      );
    } catch (e) {
      return null;
    }
  }
  
  /// Vérifie si une langue est RTL (Right-to-Left)
  static bool isRTL(Locale locale) {
    return locale.languageCode == 'ar' || 
           locale.languageCode == 'he' || 
           locale.languageCode == 'fa';
  }
}

/// Classe représentant une option de langue
class LanguageOption {
  final String code;
  final String name;
  final String nativeName;
  final Locale locale;
  final String flag;
  
  const LanguageOption({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.locale,
    required this.flag,
  });
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageOption && other.code == code;
  }
  
  @override
  int get hashCode => code.hashCode;
}
