import 'dart:async';
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';

/// Service de calibration automatique de la caméra
/// Utilise la taille réelle des véhicules pour déterminer la distance et la hauteur de la caméra
class CameraCalibrationService {
  // État de calibration
  bool _isCalibrated = false;
  double? _cameraHeightMeters;
  double? _pixelsPerMeterAtGround;
  double? _focalLengthPixels;
  
  // Paramètres de calibration
  static const double _carAverageHeight = 1.5; // mètres
  static const double _motorcycleAverageHeight = 1.0; // mètres
  static const double _truckAverageHeight = 2.5; // mètres
  
  // Historique des mesures pour stabilisation
  final List<CalibrationMeasurement> _measurements = [];
  static const int _minMeasurementsForCalibration = 5;
  static const int _maxMeasurements = 20;
  
  // Stream de calibration
  final StreamController<CalibrationResult> _calibrationController = 
      StreamController<CalibrationResult>.broadcast();
  
  // Getters
  bool get isCalibrated => _isCalibrated;
  double? get cameraHeightMeters => _cameraHeightMeters;
  double? get pixelsPerMeterAtGround => _pixelsPerMeterAtGround;
  Stream<CalibrationResult> get calibrationStream => _calibrationController.stream;
  
  /// Ajoute une mesure de véhicule pour la calibration
  void addVehicleMeasurement(VehicleData vehicle, Size imageSize) {
    if (_isCalibrated && _measurements.length >= _maxMeasurements) {
      return; // Calibration stable, pas besoin de plus de mesures
    }
    
    // Estimer le type de véhicule basé sur la taille de la bounding box
    final vehicleType = _estimateVehicleType(vehicle.bbox, imageSize);
    final realHeight = _getRealVehicleHeight(vehicleType);
    
    // Calculer la distance estimée basée sur la perspective
    final distanceEstimate = _calculateDistanceFromPerspective(
      vehicle.bbox, 
      imageSize, 
      realHeight
    );
    
    if (distanceEstimate != null) {
      final measurement = CalibrationMeasurement(
        vehicleType: vehicleType,
        bbox: vehicle.bbox,
        estimatedDistance: distanceEstimate,
        realHeight: realHeight,
        timestamp: DateTime.now(),
        confidence: vehicle.confidence,
      );
      
      _measurements.add(measurement);
      
      // Limiter le nombre de mesures
      if (_measurements.length > _maxMeasurements) {
        _measurements.removeAt(0);
      }
      
      // Tenter la calibration si on a assez de mesures
      if (_measurements.length >= _minMeasurementsForCalibration) {
        _attemptCalibration(imageSize);
      }
    }
  }
  
  /// Estime le type de véhicule basé sur la taille de la bounding box
  VehicleType _estimateVehicleType(BoundingBox bbox, Size imageSize) {
    final heightPixels = bbox.height * imageSize.height;
    final widthPixels = bbox.width * imageSize.width;
    final aspectRatio = widthPixels / heightPixels;
    
    // Heuristiques basées sur la taille et le ratio
    if (heightPixels < 60 || aspectRatio > 3.0) {
      return VehicleType.motorcycle;
    } else if (heightPixels > 120 || aspectRatio < 1.5) {
      return VehicleType.truck;
    } else {
      return VehicleType.car;
    }
  }
  
  /// Obtient la hauteur réelle d'un type de véhicule
  double _getRealVehicleHeight(VehicleType type) {
    switch (type) {
      case VehicleType.car:
        return _carAverageHeight;
      case VehicleType.motorcycle:
        return _motorcycleAverageHeight;
      case VehicleType.truck:
        return _truckAverageHeight;
    }
  }
  
  /// Calcule la distance estimée basée sur la perspective
  double? _calculateDistanceFromPerspective(
    BoundingBox bbox, 
    Size imageSize, 
    double realHeight
  ) {
    // Utiliser la hauteur de la bounding box pour estimer la distance
    final heightPixels = bbox.height * imageSize.height;
    
    if (heightPixels < 10) return null; // Trop petit pour être fiable
    
    // Estimation basique de la distance (sera affinée avec plus de mesures)
    // Distance = (hauteur_réelle * focale_estimée) / hauteur_pixels
    final estimatedFocalLength = imageSize.height * 0.7; // Estimation initiale
    final distance = (realHeight * estimatedFocalLength) / heightPixels;
    
    return distance;
  }
  
  /// Tente de calibrer la caméra avec les mesures disponibles
  void _attemptCalibration(Size imageSize) {
    if (_measurements.length < _minMeasurementsForCalibration) return;
    
    // Filtrer les mesures par confiance
    final goodMeasurements = _measurements
        .where((m) => m.confidence > 0.6)
        .toList();
    
    if (goodMeasurements.length < 3) return;
    
    // Calculer la hauteur moyenne de la caméra
    final cameraHeights = <double>[];
    final pixelsPerMeterValues = <double>[];
    
    for (final measurement in goodMeasurements) {
      // Calculer la hauteur de la caméra basée sur cette mesure
      final cameraHeight = _calculateCameraHeight(measurement, imageSize);
      if (cameraHeight != null && cameraHeight > 0.5 && cameraHeight < 10.0) {
        cameraHeights.add(cameraHeight);
        
        // Calculer les pixels par mètre au sol
        final pixelsPerMeter = _calculatePixelsPerMeter(measurement, imageSize, cameraHeight);
        if (pixelsPerMeter != null) {
          pixelsPerMeterValues.add(pixelsPerMeter);
        }
      }
    }
    
    if (cameraHeights.isNotEmpty && pixelsPerMeterValues.isNotEmpty) {
      // Utiliser la médiane pour plus de robustesse
      cameraHeights.sort();
      pixelsPerMeterValues.sort();
      
      _cameraHeightMeters = _getMedian(cameraHeights);
      _pixelsPerMeterAtGround = _getMedian(pixelsPerMeterValues);
      
      // Calculer la focale approximative
      _focalLengthPixels = imageSize.height * 0.7; // Estimation basique
      
      _isCalibrated = true;
      
      // Émettre le résultat de calibration
      final result = CalibrationResult(
        cameraHeight: _cameraHeightMeters!,
        pixelsPerMeterAtGround: _pixelsPerMeterAtGround!,
        focalLength: _focalLengthPixels!,
        confidence: _calculateCalibrationConfidence(),
        measurementCount: goodMeasurements.length,
      );
      
      _calibrationController.add(result);
      
      print('Calibration réussie: hauteur=${_cameraHeightMeters!.toStringAsFixed(2)}m, '
            'pixels/m=${_pixelsPerMeterAtGround!.toStringAsFixed(1)}');
    }
  }
  
  /// Calcule la hauteur de la caméra basée sur une mesure
  double? _calculateCameraHeight(CalibrationMeasurement measurement, Size imageSize) {
    // Utiliser la géométrie de perspective pour calculer la hauteur
    final heightPixels = measurement.bbox.height * imageSize.height;
    final bottomY = (measurement.bbox.y + measurement.bbox.height) * imageSize.height;
    
    // Distance du centre de l'image au bas du véhicule
    final distanceFromCenter = bottomY - (imageSize.height / 2);
    
    // Angle de dépression approximatif
    final angleRad = math.atan(distanceFromCenter / (imageSize.height * 0.7));
    
    // Hauteur de la caméra = distance * tan(angle) + hauteur_véhicule
    final cameraHeight = measurement.estimatedDistance * math.tan(angleRad.abs()) + 
                        measurement.realHeight;
    
    return cameraHeight > 0 ? cameraHeight : null;
  }
  
  /// Calcule les pixels par mètre au sol
  double? _calculatePixelsPerMeter(
    CalibrationMeasurement measurement, 
    Size imageSize, 
    double cameraHeight
  ) {
    // Utiliser la distance estimée et la position dans l'image
    final heightPixels = measurement.bbox.height * imageSize.height;
    return heightPixels / measurement.realHeight;
  }
  
  /// Calcule la médiane d'une liste
  double _getMedian(List<double> values) {
    values.sort();
    final middle = values.length ~/ 2;
    if (values.length % 2 == 0) {
      return (values[middle - 1] + values[middle]) / 2;
    } else {
      return values[middle];
    }
  }
  
  /// Calcule la confiance de la calibration
  double _calculateCalibrationConfidence() {
    if (_measurements.isEmpty) return 0.0;
    
    final avgConfidence = _measurements
        .map((m) => m.confidence)
        .reduce((a, b) => a + b) / _measurements.length;
    
    // Facteur basé sur le nombre de mesures
    final countFactor = math.min(1.0, _measurements.length / _maxMeasurements);
    
    return avgConfidence * countFactor;
  }
  
  /// Convertit une distance en pixels en distance réelle (mètres)
  double? pixelDistanceToRealDistance(double pixelDistance, double averageDepth) {
    if (!_isCalibrated || _pixelsPerMeterAtGround == null) return null;
    
    // Ajuster selon la profondeur (les objets plus loin apparaissent plus petits)
    final depthFactor = averageDepth / 10.0; // Normaliser à 10m de référence
    final adjustedPixelsPerMeter = _pixelsPerMeterAtGround! / depthFactor;
    
    return pixelDistance / adjustedPixelsPerMeter;
  }
  
  /// Réinitialise la calibration
  void resetCalibration() {
    _isCalibrated = false;
    _cameraHeightMeters = null;
    _pixelsPerMeterAtGround = null;
    _focalLengthPixels = null;
    _measurements.clear();
  }
  
  /// Libère les ressources
  void dispose() {
    _calibrationController.close();
    _measurements.clear();
  }
}

/// Types de véhicules pour la calibration
enum VehicleType {
  car,
  motorcycle,
  truck,
}

/// Mesure de calibration
class CalibrationMeasurement {
  final VehicleType vehicleType;
  final BoundingBox bbox;
  final double estimatedDistance;
  final double realHeight;
  final DateTime timestamp;
  final double confidence;
  
  const CalibrationMeasurement({
    required this.vehicleType,
    required this.bbox,
    required this.estimatedDistance,
    required this.realHeight,
    required this.timestamp,
    required this.confidence,
  });
}

/// Résultat de calibration
class CalibrationResult {
  final double cameraHeight;
  final double pixelsPerMeterAtGround;
  final double focalLength;
  final double confidence;
  final int measurementCount;
  
  const CalibrationResult({
    required this.cameraHeight,
    required this.pixelsPerMeterAtGround,
    required this.focalLength,
    required this.confidence,
    required this.measurementCount,
  });
}
