import 'dart:async';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/services.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';

/// Service de gestion de la caméra et acquisition des données
class CameraService {
  static const MethodChannel _channel = MethodChannel('radar_speed_detector/camera');
  
  CameraController? _controller;
  StreamController<CameraImage>? _imageStreamController;
  StreamController<DepthData>? _depthStreamController;
  
  bool _isInitialized = false;
  bool _isStreaming = false;
  
  // Getters pour les streams
  Stream<CameraImage> get imageStream => _imageStreamController?.stream ?? const Stream.empty();
  Stream<DepthData> get depthStream => _depthStreamController?.stream ?? const Stream.empty();
  
  bool get isInitialized => _isInitialized;
  bool get isStreaming => _isStreaming;
  
  /// Initialise le service caméra
  Future<void> initialize() async {
    try {
      // Obtenir les caméras disponibles
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }
      
      // Utiliser la caméra arrière par défaut (priorité absolue)
      CameraDescription? backCamera;
      CameraDescription? frontCamera;

      // Séparer les caméras par type
      for (final camera in cameras) {
        if (camera.lensDirection == CameraLensDirection.back) {
          backCamera = camera;
        } else if (camera.lensDirection == CameraLensDirection.front) {
          frontCamera = camera;
        }
      }

      // Priorité absolue à la caméra arrière
      final selectedCamera = backCamera ?? frontCamera ?? cameras.first;

      if (backCamera == null) {
        print('Attention: Caméra arrière non disponible, utilisation de: ${selectedCamera.lensDirection}');
      } else {
        print('Caméra arrière sélectionnée avec succès');
      }
      
      // Initialiser le contrôleur de caméra
      _controller = CameraController(
        selectedCamera,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );
      
      await _controller!.initialize();
      
      // Initialiser les streams
      _imageStreamController = StreamController<CameraImage>.broadcast();
      _depthStreamController = StreamController<DepthData>.broadcast();
      
      // Configurer les platform channels pour ARCore/ARKit
      await _setupNativeChannels();
      
      _isInitialized = true;
    } catch (e) {
      throw Exception('Erreur lors de l\'initialisation de la caméra: $e');
    }
  }
  
  /// Configure les platform channels natifs
  Future<void> _setupNativeChannels() async {
    _channel.setMethodCallHandler(_handleNativeCall);

    // Initialiser ARCore/ARKit (optionnel - peut échouer si non implémenté)
    try {
      await _channel.invokeMethod('initializeAR');
      print('ARCore/ARKit initialisé avec succès');
    } catch (e) {
      print('ARCore/ARKit non disponible, utilisation du mode caméra standard: $e');
      // Continuer sans AR - ce n'est pas critique pour le fonctionnement de base
    }
  }
  
  /// Gère les appels depuis le code natif
  Future<void> _handleNativeCall(MethodCall call) async {
    switch (call.method) {
      case 'onDepthDataReceived':
        _handleDepthData(call.arguments);
        break;
      case 'onARSessionFailed':
        throw Exception('Session AR échouée: ${call.arguments}');
      default:
        break;
    }
  }
  
  /// Traite les données de profondeur reçues
  void _handleDepthData(Map<String, dynamic> data) {
    try {
      final depthMap = _parseDepthMap(data['depthMap']);
      final confidenceMap = _parseConfidenceMap(data['confidenceMap']);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(data['timestamp']);
      final intrinsics = CameraIntrinsics(
        fx: data['fx'].toDouble(),
        fy: data['fy'].toDouble(),
        cx: data['cx'].toDouble(),
        cy: data['cy'].toDouble(),
      );
      
      final depthData = DepthData(
        depthMap: depthMap,
        confidenceMap: confidenceMap,
        timestamp: timestamp,
        intrinsics: intrinsics,
      );
      
      _depthStreamController?.add(depthData);
    } catch (e) {
      print('Erreur lors du traitement des données de profondeur: $e');
    }
  }
  
  /// Parse la depth map depuis les données natives
  List<List<double>> _parseDepthMap(List<dynamic> rawData) {
    // Implémentation simplifiée - à adapter selon le format des données
    final List<List<double>> depthMap = [];
    // TODO: Parser les données de profondeur selon le format ARCore/ARKit
    return depthMap;
  }
  
  /// Parse la confidence map depuis les données natives
  List<List<double>> _parseConfidenceMap(List<dynamic> rawData) {
    // Implémentation simplifiée - à adapter selon le format des données
    final List<List<double>> confidenceMap = [];
    // TODO: Parser les données de confiance selon le format ARCore/ARKit
    return confidenceMap;
  }
  
  /// Démarre le streaming de la caméra
  Future<void> startStreaming() async {
    if (!_isInitialized || _controller == null) {
      throw Exception('Caméra non initialisée');
    }
    
    if (_isStreaming) return;
    
    try {
      // Démarrer le stream d'images
      await _controller!.startImageStream((CameraImage image) {
        _imageStreamController?.add(image);
      });
      
      // Démarrer la session AR native (optionnel)
      try {
        await _channel.invokeMethod('startARSession');
        print('Session AR démarrée');
      } catch (e) {
        print('Session AR non disponible, mode caméra standard: $e');
        // Continuer sans AR
      }
      
      _isStreaming = true;
    } catch (e) {
      throw Exception('Erreur lors du démarrage du streaming: $e');
    }
  }
  
  /// Arrête le streaming de la caméra
  Future<void> stopStreaming() async {
    if (!_isStreaming) return;
    
    try {
      await _controller?.stopImageStream();

      // Arrêter la session AR native (optionnel)
      try {
        await _channel.invokeMethod('stopARSession');
        print('Session AR arrêtée');
      } catch (e) {
        print('Pas de session AR à arrêter: $e');
        // Continuer sans erreur
      }

      _isStreaming = false;
    } catch (e) {
      print('Erreur lors de l\'arrêt du streaming: $e');
    }
  }
  
  /// Capture une photo haute résolution
  Future<XFile> capturePhoto() async {
    if (!_isInitialized || _controller == null) {
      throw Exception('Caméra non initialisée');
    }
    
    return await _controller!.takePicture();
  }
  
  /// Obtient les paramètres intrinsèques de la caméra
  Future<CameraIntrinsics> getCameraIntrinsics() async {
    final result = await _channel.invokeMethod('getCameraIntrinsics');
    return CameraIntrinsics(
      fx: result['fx'].toDouble(),
      fy: result['fy'].toDouble(),
      cx: result['cx'].toDouble(),
      cy: result['cy'].toDouble(),
    );
  }
  
  /// Libère les ressources
  Future<void> dispose() async {
    await stopStreaming();
    await _controller?.dispose();
    await _imageStreamController?.close();
    await _depthStreamController?.close();
    _isInitialized = false;
  }
}
