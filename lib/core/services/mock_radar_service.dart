import 'dart:async';
import 'dart:math' as math;
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';
import 'radar_service.dart';

/// Service radar simulé pour les tests
class MockRadarService {
  // Streams
  final StreamController<List<VehicleData>> _vehicleStreamController = 
      StreamController<List<VehicleData>>.broadcast();
  final StreamController<OverspeedEvent> _overspeedStreamController = 
      StreamController<OverspeedEvent>.broadcast();
  
  // État
  bool _isInitialized = false;
  bool _isRunning = false;
  UserSettings _settings = const UserSettings();
  
  // Simulation
  Timer? _simulationTimer;
  final List<VehicleData> _simulatedVehicles = [];
  int _vehicleIdCounter = 1;
  final math.Random _random = math.Random();
  
  // Getters
  Stream<List<VehicleData>> get vehicleStream => _vehicleStreamController.stream;
  Stream<OverspeedEvent> get overspeedStream => _overspeedStreamController.stream;
  bool get isInitialized => _isInitialized;
  bool get isRunning => _isRunning;
  UserSettings get settings => _settings;
  
  /// Initialise le service simulé
  Future<void> initialize() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simule l'initialisation
    _isInitialized = true;
  }
  
  /// Démarre la simulation
  Future<void> start() async {
    if (!_isInitialized) {
      throw Exception('Service non initialisé');
    }
    
    if (_isRunning) return;
    
    _isRunning = true;
    _startSimulation();
  }
  
  /// Arrête la simulation
  Future<void> stop() async {
    if (!_isRunning) return;
    
    _isRunning = false;
    _simulationTimer?.cancel();
    _simulatedVehicles.clear();
  }
  
  /// Démarre la simulation des véhicules
  void _startSimulation() {
    _simulationTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isRunning) {
        timer.cancel();
        return;
      }
      
      _updateSimulation();
    });
  }
  
  /// Met à jour la simulation
  void _updateSimulation() {
    // Ajouter de nouveaux véhicules aléatoirement
    if (_random.nextDouble() < 0.02 && _simulatedVehicles.length < 5) {
      _addRandomVehicle();
    }
    
    // Mettre à jour les véhicules existants
    final updatedVehicles = <VehicleData>[];
    
    for (final vehicle in _simulatedVehicles) {
      final updatedVehicle = _updateVehicle(vehicle);
      if (updatedVehicle != null) {
        updatedVehicles.add(updatedVehicle);
      }
    }
    
    _simulatedVehicles.clear();
    _simulatedVehicles.addAll(updatedVehicles);
    
    // Émettre les véhicules mis à jour
    _vehicleStreamController.add(List.from(_simulatedVehicles));
  }
  
  /// Ajoute un véhicule aléatoire (mouvement horizontal)
  void _addRandomVehicle() {
    final vehicle = VehicleData(
      id: 'vehicle_${_vehicleIdCounter++}',
      bbox: BoundingBox(
        x: 0.05, // Commence à gauche de l'écran
        y: _random.nextDouble() * 0.6 + 0.2, // Position verticale aléatoire
        width: 0.15 + _random.nextDouble() * 0.1, // Entre 0.15 et 0.25
        height: 0.08 + _random.nextDouble() * 0.05, // Plus petit en hauteur pour véhicules horizontaux
      ),
      confidence: 0.8 + _random.nextDouble() * 0.2,
      timestamp: DateTime.now(),
      depth: 25.0 + _random.nextDouble() * 5.0, // Entre 25 et 30m
      state: VehicleState.beforeA,
    );

    _simulatedVehicles.add(vehicle);
  }
  
  /// Met à jour un véhicule (mouvement horizontal)
  VehicleData? _updateVehicle(VehicleData vehicle) {
    // Simuler le mouvement vers la droite (circulation horizontale)
    final newX = vehicle.bbox.x + 0.008; // Mouvement vers la droite
    final newDepth = math.max(1.0, vehicle.depth! - 0.15); // Se rapproche légèrement

    // Supprimer le véhicule s'il sort de l'écran à droite
    if (newX > 1.0) {
      return null;
    }
    
    // Mettre à jour l'état selon la profondeur
    VehicleState newState = vehicle.state;
    DateTime? portiqueATime = vehicle.portiqueATime;
    DateTime? portiqueBTime = vehicle.portiqueBTime;
    double? speedKmh = vehicle.speedKmh;
    
    final now = DateTime.now();
    
    if (vehicle.state == VehicleState.beforeA && newDepth <= AppConstants.portique_A_distance) {
      newState = VehicleState.betweenAB;
      portiqueATime = now;
    } else if (vehicle.state == VehicleState.betweenAB && newDepth <= AppConstants.portique_B_distance) {
      newState = VehicleState.done;
      portiqueBTime = now;
      
      // Calculer la vitesse
      if (portiqueATime != null) {
        final deltaTime = portiqueBTime!.difference(portiqueATime);
        final deltaTimeSeconds = deltaTime.inMicroseconds / 1000000.0;
        
        if (deltaTimeSeconds > 0) {
          final speedMs = AppConstants.distance_between_portiques / deltaTimeSeconds;
          speedKmh = speedMs * AppConstants.speed_conversion_factor;
          
          // Ajouter de la variation réaliste
          speedKmh = speedKmh! + (_random.nextDouble() - 0.5) * 10;
          speedKmh = math.max(20.0, math.min(120.0, speedKmh));
          
          // Vérifier le dépassement de vitesse
          if (speedKmh > _settings.speedLimitKmh + _settings.toleranceKmh) {
            _simulateOverspeed(vehicle.id, speedKmh, now);
          }
        }
      }
    }
    
    return vehicle.copyWith(
      bbox: BoundingBox(
        x: newX, // Position X mise à jour pour mouvement horizontal
        y: vehicle.bbox.y, // Position Y reste la même
        width: vehicle.bbox.width,
        height: vehicle.bbox.height,
      ),
      depth: newDepth,
      state: newState,
      portiqueATime: portiqueATime,
      portiqueBTime: portiqueBTime,
      speedKmh: speedKmh,
      timestamp: now,
    );
  }
  
  /// Simule un dépassement de vitesse
  void _simulateOverspeed(String vehicleId, double speedKmh, DateTime timestamp) {
    final overspeedEvent = OverspeedEvent(
      vehicleId: vehicleId,
      measuredSpeed: speedKmh,
      speedLimit: _settings.speedLimitKmh,
      timestamp: timestamp,
      photoPath: null, // Pas de photo en simulation
      licensePlate: _generateRandomPlate(),
      confidence: 0.8 + _random.nextDouble() * 0.2,
    );
    
    _overspeedStreamController.add(overspeedEvent);
  }
  
  /// Génère une plaque d'immatriculation aléatoire
  String _generateRandomPlate() {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    
    String plate = '';
    
    // Format français: AB-123-CD
    for (int i = 0; i < 2; i++) {
      plate += letters[_random.nextInt(letters.length)];
    }
    plate += '-';
    for (int i = 0; i < 3; i++) {
      plate += numbers[_random.nextInt(numbers.length)];
    }
    plate += '-';
    for (int i = 0; i < 2; i++) {
      plate += letters[_random.nextInt(letters.length)];
    }
    
    return plate;
  }
  
  /// Met à jour les paramètres utilisateur
  void updateSettings(UserSettings newSettings) {
    _settings = newSettings;
  }
  
  /// Obtient les statistiques actuelles
  RadarStats getStats() {
    final totalVehicles = _simulatedVehicles.length;
    final completedMeasurements = _simulatedVehicles
        .where((v) => v.state == VehicleState.done)
        .length;
    
    double? averageSpeed;
    final speeds = _simulatedVehicles
        .where((v) => v.speedKmh != null)
        .map((v) => v.speedKmh!)
        .toList();
    
    if (speeds.isNotEmpty) {
      averageSpeed = speeds.reduce((a, b) => a + b) / speeds.length;
    }
    
    return RadarStats(
      totalVehicles: totalVehicles,
      completedMeasurements: completedMeasurements,
      averageSpeed: averageSpeed,
      isRunning: _isRunning,
    );
  }
  
  /// Libère les ressources
  Future<void> dispose() async {
    await stop();
    await _vehicleStreamController.close();
    await _overspeedStreamController.close();
    _isInitialized = false;
  }
}
