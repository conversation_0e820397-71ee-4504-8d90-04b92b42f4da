import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';
import 'camera_calibration_service.dart';

/// Service de calcul de vitesse basé sur la perspective et le mouvement en pixels
class PerspectiveSpeedService {
  final CameraCalibrationService _calibrationService;
  
  // Historique des positions par véhicule
  final Map<String, List<VehiclePosition>> _positionHistory = {};
  
  // États des véhicules pour le calcul de vitesse
  final Map<String, VehicleSpeedState> _vehicleStates = {};
  
  // Stream des événements de vitesse
  final StreamController<SpeedEvent> _speedEventController = 
      StreamController<SpeedEvent>.broadcast();
  
  Stream<SpeedEvent> get speedEventStream => _speedEventController.stream;
  
  PerspectiveSpeedService(this._calibrationService);
  
  /// Traite une nouvelle position de véhicule
  void processVehiclePosition(VehicleData vehicle, Size imageSize) {
    final vehicleId = vehicle.id;
    final timestamp = vehicle.timestamp;
    
    // Créer la position
    final position = VehiclePosition(
      bbox: vehicle.bbox,
      timestamp: timestamp,
      imageSize: imageSize,
    );
    
    // Ajouter à l'historique
    _positionHistory.putIfAbsent(vehicleId, () => []);
    _positionHistory[vehicleId]!.add(position);
    
    // Limiter la taille de l'historique (garder les 10 dernières positions)
    if (_positionHistory[vehicleId]!.length > 10) {
      _positionHistory[vehicleId]!.removeAt(0);
    }
    
    // Obtenir ou créer l'état du véhicule
    final state = _vehicleStates.putIfAbsent(vehicleId, () => VehicleSpeedState(
      vehicleId: vehicleId,
    ));
    
    // Calculer la vitesse si on a assez d'historique
    if (_positionHistory[vehicleId]!.length >= 2) {
      _calculateSpeed(vehicleId, state);
    }
  }
  
  /// Calcule la vitesse d'un véhicule basée sur son mouvement
  void _calculateSpeed(String vehicleId, VehicleSpeedState state) {
    final positions = _positionHistory[vehicleId]!;
    if (positions.length < 2) return;
    
    // Utiliser les deux dernières positions pour le calcul
    final currentPos = positions.last;
    final previousPos = positions[positions.length - 2];
    
    // Calculer le déplacement en pixels
    final pixelDisplacement = _calculatePixelDisplacement(previousPos, currentPos);
    
    // Calculer le temps écoulé
    final deltaTime = currentPos.timestamp.difference(previousPos.timestamp);
    final deltaTimeSeconds = deltaTime.inMicroseconds / 1000000.0;
    
    if (deltaTimeSeconds <= 0 || pixelDisplacement <= 0) return;
    
    // Convertir le déplacement en pixels en distance réelle
    final realDistance = _convertPixelDistanceToReal(
      pixelDisplacement, 
      currentPos,
    );
    
    if (realDistance != null && realDistance > 0) {
      // Calculer la vitesse en m/s puis convertir en km/h
      final speedMs = realDistance / deltaTimeSeconds;
      final speedKmh = speedMs * AppConstants.speed_conversion_factor;
      
      // Appliquer un lissage EMA
      final smoothedSpeed = _applyEMASmoothing(vehicleId, speedKmh);
      
      // Clamp la vitesse dans les limites raisonnables
      final clampedSpeed = math.max(
        AppConstants.min_speed_kmh,
        math.min(AppConstants.max_speed_kmh, smoothedSpeed),
      );
      
      // Mettre à jour l'état
      state.lastSpeed = clampedSpeed;
      state.lastUpdate = currentPos.timestamp;
      
      // Émettre l'événement de vitesse
      _speedEventController.add(SpeedEvent(
        vehicleId: vehicleId,
        eventType: RadarEvent.speedCalculated,
        timestamp: currentPos.timestamp,
        speedKmh: clampedSpeed,
        deltaTimeMs: deltaTime.inMilliseconds,
        pixelDisplacement: pixelDisplacement,
        realDistance: realDistance,
      ));
    }
  }
  
  /// Calcule le déplacement en pixels entre deux positions
  double _calculatePixelDisplacement(VehiclePosition prev, VehiclePosition current) {
    // Utiliser le centre de la bounding box pour plus de précision
    final prevCenterX = prev.bbox.centerX * prev.imageSize.width;
    final prevCenterY = prev.bbox.centerY * prev.imageSize.height;
    
    final currentCenterX = current.bbox.centerX * current.imageSize.width;
    final currentCenterY = current.bbox.centerY * current.imageSize.height;
    
    // Calculer la distance euclidienne
    final dx = currentCenterX - prevCenterX;
    final dy = currentCenterY - prevCenterY;
    
    return math.sqrt(dx * dx + dy * dy);
  }
  
  /// Convertit une distance en pixels en distance réelle
  double? _convertPixelDistanceToReal(double pixelDistance, VehiclePosition position) {
    if (!_calibrationService.isCalibrated) return null;
    
    // Estimer la distance du véhicule à la caméra basée sur sa position dans l'image
    final estimatedDepth = _estimateVehicleDepth(position);
    
    // Utiliser la calibration pour convertir
    return _calibrationService.pixelDistanceToRealDistance(pixelDistance, estimatedDepth);
  }
  
  /// Estime la profondeur d'un véhicule basée sur sa position dans l'image
  double _estimateVehicleDepth(VehiclePosition position) {
    // Utiliser la position Y pour estimer la profondeur
    // Plus le véhicule est bas dans l'image, plus il est proche
    final normalizedY = position.bbox.centerY;
    
    // Mapper la position Y à une distance estimée (heuristique)
    // Y=0 (haut) -> distance lointaine, Y=1 (bas) -> distance proche
    final minDistance = 5.0; // mètres
    final maxDistance = 50.0; // mètres
    
    // Fonction inverse: plus Y est grand, plus la distance est petite
    final distance = maxDistance - (normalizedY * (maxDistance - minDistance));
    
    return math.max(minDistance, distance);
  }
  
  /// Applique un lissage EMA (Exponential Moving Average) à la vitesse
  double _applyEMASmoothing(String vehicleId, double newSpeed) {
    final state = _vehicleStates[vehicleId];
    if (state == null) return newSpeed;
    
    if (state.smoothedSpeed == null) {
      state.smoothedSpeed = newSpeed;
      return newSpeed;
    }
    
    // Calculer l'alpha basé sur la fenêtre temporelle
    const alpha = 0.3; // Facteur de lissage (0 = pas de lissage, 1 = pas de mémoire)
    state.smoothedSpeed = alpha * newSpeed + (1 - alpha) * state.smoothedSpeed!;
    
    return state.smoothedSpeed!;
  }
  
  /// Obtient l'état actuel d'un véhicule
  VehicleSpeedState? getVehicleState(String vehicleId) {
    return _vehicleStates[vehicleId];
  }
  
  /// Obtient tous les états des véhicules
  Map<String, VehicleSpeedState> getAllVehicleStates() {
    return Map.from(_vehicleStates);
  }
  
  /// Nettoie les données des véhicules anciens
  void cleanupOldVehicles() {
    final now = DateTime.now();
    final idsToRemove = <String>[];
    
    for (final entry in _vehicleStates.entries) {
      final state = entry.value;
      
      if (state.lastUpdate != null) {
        final timeSinceUpdate = now.difference(state.lastUpdate!).inMilliseconds;
        if (timeSinceUpdate > AppConstants.vehicle_timeout_ms) {
          idsToRemove.add(entry.key);
        }
      }
    }
    
    for (final id in idsToRemove) {
      _vehicleStates.remove(id);
      _positionHistory.remove(id);
    }
  }
  
  /// Réinitialise l'état d'un véhicule
  void resetVehicleState(String vehicleId) {
    _vehicleStates.remove(vehicleId);
    _positionHistory.remove(vehicleId);
  }
  
  /// Libère les ressources
  void dispose() {
    _speedEventController.close();
    _vehicleStates.clear();
    _positionHistory.clear();
  }
}

/// Position d'un véhicule à un moment donné
class VehiclePosition {
  final BoundingBox bbox;
  final DateTime timestamp;
  final Size imageSize;
  
  const VehiclePosition({
    required this.bbox,
    required this.timestamp,
    required this.imageSize,
  });
}

/// État de calcul de vitesse pour un véhicule (version perspective)
class VehicleSpeedState {
  final String vehicleId;
  double? lastSpeed;
  double? smoothedSpeed;
  DateTime? lastUpdate;
  
  VehicleSpeedState({
    required this.vehicleId,
    this.lastSpeed,
    this.smoothedSpeed,
    this.lastUpdate,
  });
}

/// Événement de vitesse étendu
class SpeedEvent {
  final String vehicleId;
  final RadarEvent eventType;
  final DateTime timestamp;
  final double? speedKmh;
  final int? deltaTimeMs;
  final double? pixelDisplacement;
  final double? realDistance;
  
  const SpeedEvent({
    required this.vehicleId,
    required this.eventType,
    required this.timestamp,
    this.speedKmh,
    this.deltaTimeMs,
    this.pixelDisplacement,
    this.realDistance,
  });
}
