import 'dart:async';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';
import 'camera_service.dart';
import 'depth_service.dart';
import 'ml_vehicle_detection_service.dart';
import 'speed_calculation_service.dart';
import 'perspective_speed_service.dart' as perspective;
import 'license_plate_detection_service.dart';
import 'driver_detection_service.dart';

/// Service principal qui orchestre tous les autres services
class RadarService {
  // Services
  final CameraService _cameraService = CameraService();
  final DepthService _depthService = DepthService();
  final MLVehicleDetectionService _detectionService = MLVehicleDetectionService();
  final SpeedCalculationService _speedService = SpeedCalculationService();
  late final perspective.PerspectiveSpeedService _perspectiveSpeedService;
  final LicensePlateDetectionService _plateService = LicensePlateDetectionService();
  final DriverDetectionService _driverService = DriverDetectionService();
  
  // Streams
  final StreamController<List<VehicleData>> _vehicleStreamController = 
      StreamController<List<VehicleData>>.broadcast();
  final StreamController<OverspeedEvent> _overspeedStreamController = 
      StreamController<OverspeedEvent>.broadcast();
  
  // État
  bool _isInitialized = false;
  bool _isRunning = false;
  UserSettings _settings = const UserSettings();
  
  // Subscriptions
  StreamSubscription? _imageSubscription;
  StreamSubscription? _depthSubscription;
  StreamSubscription? _detectionSubscription;
  StreamSubscription? _speedSubscription;
  StreamSubscription? _perspectiveSpeedSubscription;
  StreamSubscription? _plateSubscription;
  StreamSubscription? _driverSubscription;
  
  // Getters
  Stream<List<VehicleData>> get vehicleStream => _vehicleStreamController.stream;
  Stream<OverspeedEvent> get overspeedStream => _overspeedStreamController.stream;
  bool get isInitialized => _isInitialized;
  bool get isRunning => _isRunning;
  UserSettings get settings => _settings;
  
  /// Initialise tous les services
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialiser les services dans l'ordre
      await _cameraService.initialize();
      await _detectionService.initialize();

      // Initialiser le service de vitesse perspective avec la calibration
      _perspectiveSpeedService = perspective.PerspectiveSpeedService(
        _detectionService.calibrationService
      );

      // Initialiser les services optionnels
      await _plateService.initialize();
      await _driverService.initialize();
      
      // Configurer les subscriptions
      _setupSubscriptions();
      
      _isInitialized = true;
    } catch (e) {
      throw Exception('Erreur lors de l\'initialisation du radar: $e');
    }
  }
  
  /// Configure les subscriptions aux streams
  void _setupSubscriptions() {
    // Subscription aux détections de véhicules
    _detectionSubscription = _detectionService.detectionStream.listen(
      _onVehiclesDetected,
      onError: (error) => print('Erreur détection: $error'),
    );
    
    // Subscription aux données de profondeur
    _depthSubscription = _cameraService.depthStream.listen(
      _onDepthDataReceived,
      onError: (error) => print('Erreur profondeur: $error'),
    );
    
    // Subscription aux événements de vitesse
    _speedSubscription = _speedService.speedEventStream.listen(
      _onSpeedEvent,
      onError: (error) => print('Erreur vitesse: $error'),
    );

    // Subscription au stream d'images pour la détection ML Kit
    _imageSubscription = _cameraService.imageStream.listen(
      _onCameraImageReceived,
      onError: (error) => print('Erreur image: $error'),
    );

    // Subscription aux événements de vitesse perspective
    _perspectiveSpeedSubscription = _perspectiveSpeedService.speedEventStream.listen(
      _onPerspectiveSpeedEvent,
      onError: (error) => print('Erreur vitesse perspective: $error'),
    );

    // Subscription aux détections de plaques
    _plateSubscription = _plateService.plateStream.listen(
      _onPlateDetected,
      onError: (error) => print('Erreur plaque: $error'),
    );

    // Subscription aux détections de conducteurs
    _driverSubscription = _driverService.detectionStream.listen(
      _onDriverDetected,
      onError: (error) => print('Erreur conducteur: $error'),
    );
  }
  
  /// Démarre le système radar
  Future<void> start() async {
    if (!_isInitialized) {
      throw Exception('Service non initialisé');
    }
    
    if (_isRunning) return;
    
    try {
      await _cameraService.startStreaming();
      _isRunning = true;
      
      // Démarrer le nettoyage périodique
      _startPeriodicCleanup();
    } catch (e) {
      throw Exception('Erreur lors du démarrage: $e');
    }
  }
  
  /// Arrête le système radar
  Future<void> stop() async {
    if (!_isRunning) return;
    
    try {
      await _cameraService.stopStreaming();
      _isRunning = false;
    } catch (e) {
      print('Erreur lors de l\'arrêt: $e');
    }
  }
  
  /// Traite les images de la caméra pour la détection ML Kit
  void _onCameraImageReceived(CameraImage image) async {
    try {
      // Traiter l'image avec ML Kit pour détecter les véhicules
      await _detectionService.detectVehicles(image);

      // Traiter les détections de plaques et conducteurs si activées
      final vehicles = _detectionService.getTrackedVehicles();
      final imageSize = Size(image.width.toDouble(), image.height.toDouble());

      // Traitement des plaques si activé
      if (_settings.showLicensePlatesEnabled && vehicles.isNotEmpty) {
        await _plateService.detectMultiplePlates(vehicles, image);
      }

      // Traitement des conducteurs si activé
      if (_settings.showDriversEnabled && vehicles.isNotEmpty) {
        await _driverService.detectMultipleDrivers(vehicles, image);
      }

      // Traitement de la vitesse perspective pour tous les véhicules
      for (final vehicle in vehicles) {
        _perspectiveSpeedService.processVehiclePosition(vehicle, imageSize);
      }

    } catch (e) {
      print('Erreur lors du traitement de l\'image: $e');
    }
  }

  /// Traite les véhicules détectés
  void _onVehiclesDetected(List<VehicleData> vehicles) {
    // Enrichir les véhicules avec les données de plaques et conducteurs
    final enrichedVehicles = <VehicleData>[];

    for (final vehicle in vehicles) {
      String? plateText;
      String? driverInfo;

      // Récupérer les données de plaque si disponibles
      if (_settings.showLicensePlatesEnabled) {
        final plateResult = _plateService.getCachedPlateResult(vehicle.id);
        if (plateResult != null && plateResult.confidence > 0.6) {
          plateText = plateResult.plateText;
        }
      }

      // Récupérer les données de conducteur si disponibles
      if (_settings.showDriversEnabled) {
        final driverResult = _driverService.getCachedDetectionResult(vehicle.id);
        if (driverResult != null && driverResult.confidence > 0.5) {
          driverInfo = driverResult.description;
        }
      }

      // Récupérer la vitesse du service perspective si disponible
      double? speedKmh = vehicle.speedKmh;
      final perspectiveState = _perspectiveSpeedService.getVehicleState(vehicle.id);
      if (perspectiveState?.lastSpeed != null) {
        speedKmh = perspectiveState!.lastSpeed;
      }

      // Créer le véhicule enrichi
      final enrichedVehicle = vehicle.copyWith(
        licensePlate: plateText,
        driverInfo: driverInfo,
        speedKmh: speedKmh,
      );

      enrichedVehicles.add(enrichedVehicle);
    }

    // Émettre les véhicules enrichis
    _vehicleStreamController.add(enrichedVehicles);
  }
  
  /// Traite les nouvelles données de profondeur
  void _onDepthDataReceived(DepthData depthData) {
    // Obtenir les véhicules actuellement trackés
    final trackedVehicles = _detectionService.getTrackedVehicles();
    
    // Traiter chaque véhicule
    for (final vehicle in trackedVehicles) {
      // Extraire la profondeur pour ce véhicule
      final depth = _depthService.extractMedianDepth(vehicle, depthData);
      
      if (depth != null) {
        // Mettre à jour le véhicule avec la profondeur
        final updatedVehicle = vehicle.copyWith(depth: depth);
        
        // Traiter pour le calcul de vitesse
        _speedService.processDepthMeasurement(updatedVehicle, depth);
      }
    }
  }
  
  /// Traite les événements de vitesse
  void _onSpeedEvent(SpeedEvent event) {
    if (event.eventType == RadarEvent.speedCalculated && event.speedKmh != null) {
      _checkForOverspeed(event.vehicleId, event.speedKmh!, event.timestamp);
    }
  }

  /// Traite les événements de vitesse perspective
  void _onPerspectiveSpeedEvent(dynamic event) {
    if (event.eventType == RadarEvent.speedCalculated && event.speedKmh != null) {
      _checkForOverspeed(event.vehicleId, event.speedKmh!, event.timestamp);
    }
  }

  /// Traite les détections de plaques
  void _onPlateDetected(dynamic plateResult) {
    // Les plaques sont automatiquement mises en cache dans le service
    // Elles seront récupérées lors de la mise à jour des véhicules
  }

  /// Traite les détections de conducteurs
  void _onDriverDetected(dynamic driverResult) {
    // Les détections de conducteurs sont automatiquement mises en cache
    // Elles seront récupérées lors de la mise à jour des véhicules
  }
  
  /// Vérifie si un véhicule dépasse la limite de vitesse
  void _checkForOverspeed(String vehicleId, double speedKmh, DateTime timestamp) {
    final limit = _settings.speedLimitKmh;
    final tolerance = _settings.toleranceKmh;
    
    if (speedKmh > limit + tolerance) {
      final vehicleState = _speedService.getVehicleState(vehicleId);
      
      // Vérifier si on n'a pas déjà capturé ce véhicule
      if (vehicleState != null && !vehicleState.isPhotoCaptured) {
        vehicleState.isPhotoCaptured = true;
        
        // Déclencher la capture photo et OCR
        _handleOverspeedDetection(vehicleId, speedKmh, timestamp);
      }
    }
  }
  
  /// Gère la détection d'un dépassement de vitesse
  Future<void> _handleOverspeedDetection(
    String vehicleId,
    double speedKmh,
    DateTime timestamp,
  ) async {
    try {
      // Capturer une photo
      final photo = await _cameraService.capturePhoto();
      
      // TODO: Implémenter la détection de plaque et OCR
      final licensePlate = await _performOCR(photo.path);
      
      // Créer l'événement de dépassement
      final overspeedEvent = OverspeedEvent(
        vehicleId: vehicleId,
        measuredSpeed: speedKmh,
        speedLimit: _settings.speedLimitKmh,
        timestamp: timestamp,
        photoPath: photo.path,
        licensePlate: licensePlate,
        confidence: 0.8, // TODO: Calculer la confiance réelle
      );
      
      // Émettre l'événement
      _overspeedStreamController.add(overspeedEvent);
      
    } catch (e) {
      print('Erreur lors de la capture de dépassement: $e');
    }
  }
  
  /// Effectue l'OCR sur une image (placeholder)
  Future<String?> _performOCR(String imagePath) async {
    // TODO: Implémenter l'OCR avec ML Kit
    return null;
  }
  
  /// Démarre le nettoyage périodique
  void _startPeriodicCleanup() {
    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!_isRunning) {
        timer.cancel();
        return;
      }
      
      _speedService.cleanupOldVehicles();
      _perspectiveSpeedService.cleanupOldVehicles();
      _plateService.cleanupCache();
      _driverService.cleanupCache();
    });
  }
  
  /// Met à jour les paramètres utilisateur
  void updateSettings(UserSettings newSettings) {
    _settings = newSettings;

    // Mettre à jour les services selon les nouveaux paramètres
    _plateService.setEnabled(newSettings.showLicensePlatesEnabled);
    _driverService.setEnabled(newSettings.showDriversEnabled);
  }
  
  /// Obtient les statistiques actuelles
  RadarStats getStats() {
    final vehicleStates = _speedService.getAllVehicleStates();
    
    int totalVehicles = vehicleStates.length;
    int completedMeasurements = vehicleStates.values
        .where((state) => state.state == VehicleState.done)
        .length;
    
    double? averageSpeed;
    if (completedMeasurements > 0) {
      final speeds = vehicleStates.values
          .where((state) => state.speedKmh != null)
          .map((state) => state.speedKmh!)
          .toList();
      
      if (speeds.isNotEmpty) {
        averageSpeed = speeds.reduce((a, b) => a + b) / speeds.length;
      }
    }
    
    return RadarStats(
      totalVehicles: totalVehicles,
      completedMeasurements: completedMeasurements,
      averageSpeed: averageSpeed,
      isRunning: _isRunning,
    );
  }
  
  /// Libère toutes les ressources
  Future<void> dispose() async {
    await stop();
    
    await _imageSubscription?.cancel();
    await _depthSubscription?.cancel();
    await _detectionSubscription?.cancel();
    await _speedSubscription?.cancel();
    await _perspectiveSpeedSubscription?.cancel();
    await _plateSubscription?.cancel();
    await _driverSubscription?.cancel();
    
    await _cameraService.dispose();
    await _detectionService.dispose();
    _speedService.dispose();
    _perspectiveSpeedService.dispose();
    await _plateService.dispose();
    await _driverService.dispose();
    
    await _vehicleStreamController.close();
    await _overspeedStreamController.close();
    
    _isInitialized = false;
  }
}

/// Statistiques du système radar
class RadarStats {
  final int totalVehicles;
  final int completedMeasurements;
  final double? averageSpeed;
  final bool isRunning;
  
  const RadarStats({
    required this.totalVehicles,
    required this.completedMeasurements,
    this.averageSpeed,
    required this.isRunning,
  });
}
