import 'dart:async';
import 'dart:math' as math;
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';
import 'depth_service.dart';

/// Service de calcul de vitesse des véhicules
class SpeedCalculationService {
  final DepthService _depthService = DepthService();
  
  // Historique des mesures de profondeur par véhicule
  final Map<String, List<DepthMeasurement>> _depthHistory = {};
  
  // États des véhicules
  final Map<String, VehicleSpeedState> _vehicleStates = {};
  
  // Stream des événements de vitesse
  final StreamController<SpeedEvent> _speedEventController = 
      StreamController<SpeedEvent>.broadcast();
  
  Stream<SpeedEvent> get speedEventStream => _speedEventController.stream;
  
  /// Traite une nouvelle mesure de profondeur pour un véhicule
  void processDepthMeasurement(VehicleData vehicle, double depth) {
    final vehicleId = vehicle.id;
    final timestamp = vehicle.timestamp;
    
    // Ajouter la mesure à l'historique
    _depthHistory.putIfAbsent(vehicleId, () => []);
    _depthHistory[vehicleId]!.add(DepthMeasurement(
      depth: depth,
      timestamp: timestamp,
    ));
    
    // Limiter la taille de l'historique (garder les 100 dernières mesures)
    if (_depthHistory[vehicleId]!.length > 100) {
      _depthHistory[vehicleId]!.removeAt(0);
    }
    
    // Obtenir ou créer l'état du véhicule
    final state = _vehicleStates.putIfAbsent(vehicleId, () => VehicleSpeedState(
      vehicleId: vehicleId,
      state: VehicleState.beforeA,
    ));
    
    // Traiter selon l'état actuel
    _processVehicleState(vehicleId, state, depth, timestamp);
  }
  
  /// Traite l'état d'un véhicule selon sa position
  void _processVehicleState(
    String vehicleId,
    VehicleSpeedState state,
    double depth,
    DateTime timestamp,
  ) {
    final history = _depthHistory[vehicleId]!;
    
    switch (state.state) {
      case VehicleState.beforeA:
        _processBeforePortiqueA(vehicleId, state, history);
        break;
        
      case VehicleState.betweenAB:
        _processBetweenPortiques(vehicleId, state, history);
        break;
        
      case VehicleState.done:
        // Véhicule déjà traité, ne rien faire
        break;
    }
  }
  
  /// Traite un véhicule avant le portique A
  void _processBeforePortiqueA(
    String vehicleId,
    VehicleSpeedState state,
    List<DepthMeasurement> history,
  ) {
    // Vérifier si le véhicule franchit le portique A
    final crossedA = _depthService.detectPortiqueCrossing(
      history,
      AppConstants.portique_A_distance,
      AppConstants.hysteresis_threshold,
    );
    
    if (crossedA) {
      // Calculer le temps de franchissement précis
      final crossingTime = _depthService.calculateSubframeCrossingTime(
        history,
        AppConstants.portique_A_distance,
      );
      
      if (crossingTime != null) {
        // Mettre à jour l'état
        state.state = VehicleState.betweenAB;
        state.portiqueATime = crossingTime;
        
        // Émettre l'événement
        _speedEventController.add(SpeedEvent(
          vehicleId: vehicleId,
          eventType: RadarEvent.portiqueACrossed,
          timestamp: crossingTime,
        ));
      }
    }
  }
  
  /// Traite un véhicule entre les portiques A et B
  void _processBetweenPortiques(
    String vehicleId,
    VehicleSpeedState state,
    List<DepthMeasurement> history,
  ) {
    // Vérifier si le véhicule franchit le portique B
    final crossedB = _depthService.detectPortiqueCrossing(
      history,
      AppConstants.portique_B_distance,
      AppConstants.hysteresis_threshold,
    );
    
    if (crossedB) {
      // Calculer le temps de franchissement précis
      final crossingTime = _depthService.calculateSubframeCrossingTime(
        history,
        AppConstants.portique_B_distance,
      );
      
      if (crossingTime != null && state.portiqueATime != null) {
        // Calculer la vitesse
        final deltaTime = crossingTime.difference(state.portiqueATime!);
        final deltaTimeSeconds = deltaTime.inMicroseconds / 1000000.0;
        
        if (deltaTimeSeconds > 0) {
          final speedMs = AppConstants.distance_between_portiques / deltaTimeSeconds;
          final speedKmh = speedMs * AppConstants.speed_conversion_factor;
          
          // Clamp la vitesse dans les limites raisonnables
          final clampedSpeed = math.max(
            AppConstants.min_speed_kmh,
            math.min(AppConstants.max_speed_kmh, speedKmh),
          );
          
          // Mettre à jour l'état
          state.state = VehicleState.done;
          state.portiqueBTime = crossingTime;
          state.speedKmh = clampedSpeed;
          
          // Émettre les événements
          _speedEventController.add(SpeedEvent(
            vehicleId: vehicleId,
            eventType: RadarEvent.portiqueBCrossed,
            timestamp: crossingTime,
          ));
          
          _speedEventController.add(SpeedEvent(
            vehicleId: vehicleId,
            eventType: RadarEvent.speedCalculated,
            timestamp: crossingTime,
            speedKmh: clampedSpeed,
            deltaTimeMs: deltaTime.inMilliseconds,
          ));
        }
      }
    }
  }
  
  /// Applique un lissage EMA (Exponential Moving Average) à la vitesse
  double applyEMASmoothing(String vehicleId, double newSpeed) {
    final state = _vehicleStates[vehicleId];
    if (state == null) return newSpeed;
    
    if (state.smoothedSpeed == null) {
      state.smoothedSpeed = newSpeed;
      return newSpeed;
    }
    
    // Calculer l'alpha basé sur la fenêtre temporelle
    final alpha = _calculateEMAAlpha();
    state.smoothedSpeed = alpha * newSpeed + (1 - alpha) * state.smoothedSpeed!;
    
    return state.smoothedSpeed!;
  }
  
  /// Calcule le facteur alpha pour l'EMA
  double _calculateEMAAlpha() {
    // Alpha basé sur la fenêtre de lissage désirée
    const windowMs = AppConstants.ema_window_ms;
    const targetFps = AppConstants.target_fps;
    const frameIntervalMs = 1000.0 / targetFps;
    
    return frameIntervalMs / (windowMs + frameIntervalMs);
  }
  
  /// Obtient l'état actuel d'un véhicule
  VehicleSpeedState? getVehicleState(String vehicleId) {
    return _vehicleStates[vehicleId];
  }
  
  /// Obtient tous les états des véhicules
  Map<String, VehicleSpeedState> getAllVehicleStates() {
    return Map.from(_vehicleStates);
  }
  
  /// Nettoie les données des véhicules anciens
  void cleanupOldVehicles() {
    final now = DateTime.now();
    final idsToRemove = <String>[];
    
    for (final entry in _vehicleStates.entries) {
      final state = entry.value;
      final lastUpdate = _depthHistory[entry.key]?.last.timestamp;
      
      if (lastUpdate != null) {
        final timeSinceUpdate = now.difference(lastUpdate).inMilliseconds;
        if (timeSinceUpdate > AppConstants.vehicle_timeout_ms) {
          idsToRemove.add(entry.key);
        }
      }
    }
    
    for (final id in idsToRemove) {
      _vehicleStates.remove(id);
      _depthHistory.remove(id);
    }
  }
  
  /// Réinitialise l'état d'un véhicule
  void resetVehicleState(String vehicleId) {
    _vehicleStates.remove(vehicleId);
    _depthHistory.remove(vehicleId);
  }
  
  /// Libère les ressources
  void dispose() {
    _speedEventController.close();
    _vehicleStates.clear();
    _depthHistory.clear();
  }
}

/// État de calcul de vitesse pour un véhicule
class VehicleSpeedState {
  final String vehicleId;
  VehicleState state;
  DateTime? portiqueATime;
  DateTime? portiqueBTime;
  double? speedKmh;
  double? smoothedSpeed;
  bool isPhotoCaptured;
  
  VehicleSpeedState({
    required this.vehicleId,
    required this.state,
    this.portiqueATime,
    this.portiqueBTime,
    this.speedKmh,
    this.smoothedSpeed,
    this.isPhotoCaptured = false,
  });
}

/// Événement de vitesse
class SpeedEvent {
  final String vehicleId;
  final RadarEvent eventType;
  final DateTime timestamp;
  final double? speedKmh;
  final int? deltaTimeMs;
  
  const SpeedEvent({
    required this.vehicleId,
    required this.eventType,
    required this.timestamp,
    this.speedKmh,
    this.deltaTimeMs,
  });
}
