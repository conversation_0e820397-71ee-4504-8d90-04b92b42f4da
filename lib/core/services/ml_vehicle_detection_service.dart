import 'dart:async';
import 'dart:typed_data';
import 'dart:math' as math;
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import '../models/vehicle_data.dart';
import '../constants/app_constants.dart';
import '../models/vehicle_tracker.dart';
import 'camera_calibration_service.dart';

/// Service de détection de véhicules utilisant Google ML Kit
class MLVehicleDetectionService {
  // ML Kit détecteurs
  late ObjectDetector _objectDetector;
  late ImageLabeler _imageLabeler;
  
  // État du service
  bool _isInitialized = false;
  
  // Tracking des véhicules
  final Map<String, VehicleTracker> _trackers = {};
  int _nextVehicleId = 1;

  // Service de calibration
  final CameraCalibrationService _calibrationService = CameraCalibrationService();

  // Codes couleur pour les véhicules
  final Map<String, int> _vehicleColors = {};
  int _nextColorCode = 0;
  
  // Stream de détections
  final StreamController<List<VehicleData>> _detectionStreamController = 
      StreamController<List<VehicleData>>.broadcast();
  
  // Getters
  Stream<List<VehicleData>> get detectionStream => _detectionStreamController.stream;
  bool get isInitialized => _isInitialized;
  CameraCalibrationService get calibrationService => _calibrationService;
  
  /// Initialise le service de détection avec ML Kit
  Future<void> initialize() async {
    try {
      // Configurer le détecteur d'objets ML Kit
      final objectDetectorOptions = ObjectDetectorOptions(
        mode: DetectionMode.stream,
        classifyObjects: true,
        multipleObjects: true,
      );
      _objectDetector = ObjectDetector(options: objectDetectorOptions);
      
      // Configurer le labeler d'images pour identifier les véhicules
      final imageLabelOptions = ImageLabelerOptions(
        confidenceThreshold: 0.7,
      );
      _imageLabeler = ImageLabeler(options: imageLabelOptions);
      
      _isInitialized = true;
    } catch (e) {
      throw Exception('Erreur lors de l\'initialisation de la détection ML Kit: $e');
    }
  }
  
  /// Détecte les véhicules dans une image de caméra
  Future<List<VehicleData>> detectVehicles(CameraImage image) async {
    if (!_isInitialized) {
      throw Exception('Service de détection non initialisé');
    }
    
    try {
      // Convertir CameraImage en InputImage pour ML Kit
      final inputImage = _convertCameraImageToInputImage(image);
      
      // Détecter les objets
      final detectedObjects = await _objectDetector.processImage(inputImage);
      
      // Filtrer et convertir en VehicleData avec mouvement horizontal
      final vehicles = await _processDetectedObjects(detectedObjects, image);
      
      // Mettre à jour les trackers
      _updateTrackers(vehicles);
      
      // Nettoyer les anciens trackers
      _cleanupOldTrackers();
      
      // Émettre les véhicules détectés
      _detectionStreamController.add(vehicles);
      
      return vehicles;
    } catch (e) {
      print('Erreur lors de la détection ML Kit: $e');
      return [];
    }
  }
  
  /// Convertit CameraImage en InputImage pour ML Kit
  InputImage _convertCameraImageToInputImage(CameraImage image) {
    // Convertir les plans d'image en bytes
    final allBytes = <int>[];
    for (final Plane plane in image.planes) {
      allBytes.addAll(plane.bytes);
    }
    final bytes = Uint8List.fromList(allBytes);

    // Créer les métadonnées d'image
    final Size imageSize = Size(image.width.toDouble(), image.height.toDouble());
    const InputImageRotation imageRotation = InputImageRotation.rotation0deg;
    const InputImageFormat inputImageFormat = InputImageFormat.nv21;

    // Créer l'InputImage avec la nouvelle API
    return InputImage.fromBytes(
      bytes: bytes,
      metadata: InputImageMetadata(
        size: imageSize,
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: image.planes[0].bytesPerRow,
      ),
    );
  }
  
  /// Traite les objets détectés et filtre les véhicules
  Future<List<VehicleData>> _processDetectedObjects(
    List<DetectedObject> detectedObjects, 
    CameraImage image
  ) async {
    final List<VehicleData> vehicles = [];
    final DateTime timestamp = DateTime.now();
    
    for (final detectedObject in detectedObjects) {
      // Vérifier si l'objet est un véhicule
      if (await _isVehicle(detectedObject)) {
        // Convertir les coordonnées en coordonnées normalisées (0-1)
        // CORRECTION: Mouvement horizontal au lieu de vertical
        final normalizedBbox = BoundingBox(
          x: detectedObject.boundingBox.left / image.width,
          y: detectedObject.boundingBox.top / image.height,
          width: detectedObject.boundingBox.width / image.width,
          height: detectedObject.boundingBox.height / image.height,
        );
        
        final vehicleId = _getOrCreateVehicleId(normalizedBbox, 0.8);
        final colorCode = _getOrCreateColorCode(vehicleId);

        final vehicle = VehicleData(
          id: vehicleId,
          bbox: normalizedBbox,
          confidence: 0.8, // ML Kit ne fournit pas toujours la confiance
          timestamp: timestamp,
          colorCode: colorCode,
        );

        // Ajouter à la calibration si pas encore calibrée
        if (!_calibrationService.isCalibrated) {
          _calibrationService.addVehicleMeasurement(
            vehicle,
            Size(image.width.toDouble(), image.height.toDouble())
          );
        }
        
        vehicles.add(vehicle);
      }
    }
    
    return vehicles;
  }
  
  /// Vérifie si un objet détecté est un véhicule
  Future<bool> _isVehicle(DetectedObject detectedObject) async {
    // Vérifier les labels si disponibles
    for (final label in detectedObject.labels) {
      final labelText = label.text.toLowerCase();
      if (_isVehicleLabel(labelText)) {
        return true;
      }
    }
    
    // Si pas de labels, utiliser la taille et la forme comme heuristique
    final bbox = detectedObject.boundingBox;
    final aspectRatio = bbox.width / bbox.height;
    
    // Les véhicules ont généralement un ratio largeur/hauteur entre 1.5 et 3.0
    // et une taille minimale
    return aspectRatio >= 1.2 && 
           aspectRatio <= 4.0 && 
           bbox.width > 50 && 
           bbox.height > 30;
  }
  
  /// Vérifie si un label correspond à un véhicule
  bool _isVehicleLabel(String label) {
    const vehicleLabels = [
      'car', 'truck', 'bus', 'van', 'vehicle', 'automobile',
      'voiture', 'camion', 'autobus', 'véhicule',
      'motorcycle', 'motorbike', 'scooter', 'moto',
      'taxi', 'police car', 'ambulance', 'fire truck',
    ];
    
    return vehicleLabels.any((vehicleLabel) => 
      label.contains(vehicleLabel) || vehicleLabel.contains(label));
  }
  
  /// Obtient ou crée un ID de véhicule basé sur le tracking
  String _getOrCreateVehicleId(BoundingBox bbox, double confidence) {
    // Chercher un tracker existant proche
    VehicleTracker? bestMatch;
    double bestDistance = double.infinity;
    
    for (final tracker in _trackers.values) {
      final distance = _calculateDistance(bbox, tracker.lastBbox);
      if (distance < bestDistance && distance < 0.1) { // Seuil de 10% de l'écran
        bestDistance = distance;
        bestMatch = tracker;
      }
    }
    
    if (bestMatch != null) {
      // Mettre à jour le tracker existant
      bestMatch.update(bbox, confidence, DateTime.now());
      return bestMatch.id;
    } else {
      // Créer un nouveau tracker
      final id = 'vehicle_${_nextVehicleId++}';
      _trackers[id] = VehicleTracker(
        id: id,
        initialBbox: bbox,
        initialConfidence: confidence,
        timestamp: DateTime.now(),
      );
      return id;
    }
  }
  
  /// Calcule la distance entre deux bounding boxes (coordonnées normalisées)
  double _calculateDistance(BoundingBox bbox1, BoundingBox bbox2) {
    final dx = bbox1.centerX - bbox2.centerX;
    final dy = bbox1.centerY - bbox2.centerY;
    return math.sqrt(dx * dx + dy * dy);
  }
  
  /// Met à jour les trackers avec les nouvelles détections
  void _updateTrackers(List<VehicleData> vehicles) {
    final now = DateTime.now();
    
    // Marquer tous les trackers comme non mis à jour
    for (final tracker in _trackers.values) {
      tracker.isUpdated = false;
    }
    
    // Mettre à jour les trackers correspondants
    for (final vehicle in vehicles) {
      final tracker = _trackers[vehicle.id];
      if (tracker != null) {
        tracker.isUpdated = true;
        tracker.lastSeen = now;
      }
    }
  }
  
  /// Nettoie les anciens trackers
  void _cleanupOldTrackers() {
    final now = DateTime.now();
    final expiredTrackers = <String>[];
    
    for (final entry in _trackers.entries) {
      final tracker = entry.value;
      final timeSinceLastSeen = now.difference(tracker.lastSeen).inMilliseconds;
      
      if (timeSinceLastSeen > AppConstants.vehicle_timeout_ms) {
        expiredTrackers.add(entry.key);
      }
    }
    
    for (final id in expiredTrackers) {
      _trackers.remove(id);
    }
  }

  /// Obtient ou crée un code couleur pour un véhicule
  int _getOrCreateColorCode(String vehicleId) {
    if (_vehicleColors.containsKey(vehicleId)) {
      return _vehicleColors[vehicleId]!;
    }

    final colorCode = _nextColorCode;
    _vehicleColors[vehicleId] = colorCode;
    _nextColorCode = (_nextColorCode + 1) % 12; // 12 couleurs disponibles

    return colorCode;
  }

  /// Obtient les véhicules actuellement trackés
  List<VehicleData> getTrackedVehicles() {
    final now = DateTime.now();
    return _trackers.values
        .where((tracker) => 
          now.difference(tracker.lastSeen).inMilliseconds < AppConstants.vehicle_timeout_ms)
        .map((tracker) => VehicleData(
          id: tracker.id,
          bbox: tracker.lastBbox,
          confidence: tracker.confidence,
          timestamp: tracker.lastSeen,
          colorCode: _vehicleColors[tracker.id],
        ))
        .toList();
  }
  
  /// Libère les ressources
  Future<void> dispose() async {
    await _objectDetector.close();
    await _imageLabeler.close();
    await _detectionStreamController.close();
    _calibrationService.dispose();
    _trackers.clear();
    _vehicleColors.clear();
    _isInitialized = false;
  }
}
