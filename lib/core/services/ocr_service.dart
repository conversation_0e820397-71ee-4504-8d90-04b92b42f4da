import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:image/image.dart' as img;
import 'package:camera/camera.dart';

/// Service OCR pour la reconnaissance internationale de plaques d'immatriculation
class OCRService {
  late TextRecognizer _textRecognizer;
  bool _isInitialized = false;

  /// Initialise le service OCR
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
      _isInitialized = true;
      print('OCRService initialisé avec succès');
    } catch (e) {
      print('Erreur lors de l\'initialisation d\'OCRService: $e');
      throw Exception('Impossible d\'initialiser OCRService: $e');
    }
  }

  /// Arrête le service OCR
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    try {
      await _textRecognizer.close();
      _isInitialized = false;
      print('OCRService arrêté');
    } catch (e) {
      print('Erreur lors de l\'arrêt d\'OCRService: $e');
    }
  }

  /// Reconnaît le texte d'une plaque d'immatriculation internationale dans une image
  Future<String?> recognizeLicensePlate(CameraImage cameraImage, {
    required double x,
    required double y,
    required double width,
    required double height,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Convertir CameraImage en InputImage
      final inputImage = _convertCameraImage(cameraImage);
      if (inputImage == null) return null;

      // Reconnaissance du texte
      final recognizedText = await _textRecognizer.processImage(inputImage);
      
      // Filtrer et extraire les plaques potentielles
      return _extractLicensePlate(recognizedText, x, y, width, height);
    } catch (e) {
      print('Erreur lors de la reconnaissance OCR: $e');
      return null;
    }
  }

  /// Reconnaît le texte d'une plaque internationale à partir d'un fichier image
  Future<String?> recognizeLicensePlateFromFile(File imageFile) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final inputImage = InputImage.fromFile(imageFile);
      final recognizedText = await _textRecognizer.processImage(inputImage);
      
      // Extraire la plaque la plus probable
      return _extractBestLicensePlate(recognizedText);
    } catch (e) {
      print('Erreur lors de la reconnaissance OCR depuis fichier: $e');
      return null;
    }
  }

  /// Convertit une CameraImage en InputImage
  InputImage? _convertCameraImage(CameraImage cameraImage) {
    try {
      final WriteBuffer allBytes = WriteBuffer();
      for (final Plane plane in cameraImage.planes) {
        allBytes.putUint8List(plane.bytes);
      }
      final bytes = allBytes.done().buffer.asUint8List();

      final Size imageSize = Size(
        cameraImage.width.toDouble(),
        cameraImage.height.toDouble(),
      );

      const InputImageRotation imageRotation = InputImageRotation.rotation0deg;

      final InputImageFormat inputImageFormat = InputImageFormat.nv21;

      final planeData = cameraImage.planes.map(
        (Plane plane) {
          return InputImagePlaneMetadata(
            bytesPerRow: plane.bytesPerRow,
            height: plane.height,
            width: plane.width,
          );
        },
      ).toList();

      final inputImageData = InputImageMetadata(
        size: imageSize,
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: planeData.first.bytesPerRow,
      );

      return InputImage.fromBytes(
        bytes: bytes,
        metadata: inputImageData,
      );
    } catch (e) {
      print('Erreur lors de la conversion CameraImage: $e');
      return null;
    }
  }

  /// Extrait une plaque d'immatriculation dans une région spécifique
  String? _extractLicensePlate(
    RecognizedText recognizedText,
    double x,
    double y,
    double width,
    double height,
  ) {
    final candidates = <String>[];

    for (final TextBlock block in recognizedText.blocks) {
      // Vérifier si le bloc est dans la région d'intérêt
      final blockRect = block.boundingBox;
      if (_isInRegion(blockRect, x, y, width, height)) {
        for (final TextLine line in block.lines) {
          final text = line.text.trim().toUpperCase();
          if (_isValidLicensePlate(text)) {
            candidates.add(text);
          }
        }
      }
    }

    // Retourner la meilleure candidate
    return candidates.isNotEmpty ? candidates.first : null;
  }

  /// Extrait la meilleure plaque d'immatriculation de tout le texte reconnu
  String? _extractBestLicensePlate(RecognizedText recognizedText) {
    final candidates = <String>[];

    for (final TextBlock block in recognizedText.blocks) {
      for (final TextLine line in block.lines) {
        final text = line.text.trim().toUpperCase();
        if (_isValidLicensePlate(text)) {
          candidates.add(text);
        }
      }
    }

    // Trier par longueur et probabilité
    candidates.sort((a, b) => _scoreLicensePlate(b).compareTo(_scoreLicensePlate(a)));
    
    return candidates.isNotEmpty ? candidates.first : null;
  }

  /// Vérifie si un rectangle est dans une région donnée
  bool _isInRegion(Rect rect, double x, double y, double width, double height) {
    return rect.left >= x &&
           rect.top >= y &&
           rect.right <= x + width &&
           rect.bottom <= y + height;
  }

  /// Vérifie si un texte ressemble à une plaque d'immatriculation internationale
  bool _isValidLicensePlate(String text) {
    // Nettoyer le texte
    final cleanText = text.replaceAll(RegExp(r'[^A-Z0-9]'), '');

    // Formats internationaux courants:
    // - Format européen: AB-123-CD (7 caractères)
    // - Format américain: ABC-1234 (7 caractères)
    // - Format général: 3-8 caractères alphanumériques

    // Vérifier la longueur minimale et maximale
    if (cleanText.length < 3 || cleanText.length > 10) {
      return false;
    }

    // Doit contenir au moins un chiffre et une lettre
    final hasDigit = RegExp(r'[0-9]').hasMatch(cleanText);
    final hasLetter = RegExp(r'[A-Z]').hasMatch(cleanText);

    // Formats courants acceptés
    final commonFormats = [
      RegExp(r'^[A-Z]{2}[0-9]{3}[A-Z]{2}$'),        // AB123CD (Europe)
      RegExp(r'^[0-9]{3,4}[A-Z]{2,3}[0-9]{2}$'),    // 1234AB12 (France ancien)
      RegExp(r'^[A-Z]{3}[0-9]{4}$'),                 // ABC1234 (USA)
      RegExp(r'^[0-9]{3}[A-Z]{3}$'),                 // 123ABC
      RegExp(r'^[A-Z]{1,3}[0-9]{1,4}[A-Z]{0,2}$'),  // Format général
    ];

    return (hasDigit && hasLetter) &&
           commonFormats.any((format) => format.hasMatch(cleanText));
  }

  /// Calcule un score pour une plaque d'immatriculation
  int _scoreLicensePlate(String plate) {
    int score = 0;
    
    // Bonus pour la longueur correcte
    if (plate.length == 7) score += 10; // Nouveau format
    else if (plate.length >= 6 && plate.length <= 8) score += 5; // Ancien format
    
    // Bonus pour les caractères alphanumériques
    score += plate.replaceAll(RegExp(r'[^A-Z0-9]'), '').length;
    
    return score;
  }

  /// Formate une plaque d'immatriculation internationale
  String formatLicensePlate(String plate) {
    final cleanPlate = plate.replaceAll(RegExp(r'[^A-Z0-9]'), '');

    // Format européen standard (7 caractères): AB-123-CD
    if (cleanPlate.length == 7 && RegExp(r'^[A-Z]{2}[0-9]{3}[A-Z]{2}$').hasMatch(cleanPlate)) {
      return '${cleanPlate.substring(0, 2)}-${cleanPlate.substring(2, 5)}-${cleanPlate.substring(5, 7)}';
    }

    // Format américain (7 caractères): ABC-1234
    if (cleanPlate.length == 7 && RegExp(r'^[A-Z]{3}[0-9]{4}$').hasMatch(cleanPlate)) {
      return '${cleanPlate.substring(0, 3)}-${cleanPlate.substring(3, 7)}';
    }

    // Format ancien français (8 caractères): 1234-AB-12
    if (cleanPlate.length == 8 && RegExp(r'^[0-9]{4}[A-Z]{2}[0-9]{2}$').hasMatch(cleanPlate)) {
      return '${cleanPlate.substring(0, 4)}-${cleanPlate.substring(4, 6)}-${cleanPlate.substring(6, 8)}';
    }

    // Format court (6 caractères): 123-ABC
    if (cleanPlate.length == 6) {
      if (RegExp(r'^[0-9]{3}[A-Z]{3}$').hasMatch(cleanPlate)) {
        return '${cleanPlate.substring(0, 3)}-${cleanPlate.substring(3, 6)}';
      } else if (RegExp(r'^[A-Z]{3}[0-9]{3}$').hasMatch(cleanPlate)) {
        return '${cleanPlate.substring(0, 3)}-${cleanPlate.substring(3, 6)}';
      }
    }

    // Retourner tel quel si format non reconnu mais valide
    return cleanPlate;
  }
}
