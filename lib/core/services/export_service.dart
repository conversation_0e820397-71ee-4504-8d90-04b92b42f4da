import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:csv/csv.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/vehicle_data.dart';

/// Service d'export des données
class ExportService {
  static const String _csvFileName = 'radar_speed_data.csv';
  static const String _imagesFolder = 'radar_images';

  /// Exporte les données de dépassement en CSV
  Future<String?> exportOverspeedData(List<OverspeedEvent> events) async {
    try {
      // Demander les permissions
      if (!await _requestStoragePermission()) {
        throw Exception('Permission de stockage refusée');
      }

      // Obtenir le répertoire de documents
      final directory = await getApplicationDocumentsDirectory();
      final csvFile = File('${directory.path}/$_csvFileName');

      // Préparer les données CSV
      final csvData = _prepareCSVData(events);

      // Écrire le fichier CSV
      final csvString = const ListToCsvConverter().convert(csvData);
      await csvFile.writeAsString(csvString);

      print('Données exportées vers: ${csvFile.path}');
      return csvFile.path;
    } catch (e) {
      print('Erreur lors de l\'export CSV: $e');
      return null;
    }
  }

  /// Sauvegarde une image de dépassement
  Future<String?> saveOverspeedImage(
    Uint8List imageData,
    String vehicleId,
    DateTime timestamp,
  ) async {
    try {
      // Demander les permissions
      if (!await _requestStoragePermission()) {
        throw Exception('Permission de stockage refusée');
      }

      // Obtenir le répertoire de documents
      final directory = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${directory.path}/$_imagesFolder');

      // Créer le dossier s'il n'existe pas
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // Nom du fichier avec timestamp
      final fileName = 'overspeed_${vehicleId}_${timestamp.millisecondsSinceEpoch}.jpg';
      final imageFile = File('${imagesDir.path}/$fileName');

      // Sauvegarder l'image
      await imageFile.writeAsBytes(imageData);

      print('Image sauvegardée: ${imageFile.path}');
      return imageFile.path;
    } catch (e) {
      print('Erreur lors de la sauvegarde d\'image: $e');
      return null;
    }
  }

  /// Obtient la liste des fichiers exportés
  Future<List<File>> getExportedFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = <File>[];

      // Fichier CSV
      final csvFile = File('${directory.path}/$_csvFileName');
      if (await csvFile.exists()) {
        files.add(csvFile);
      }

      // Images
      final imagesDir = Directory('${directory.path}/$_imagesFolder');
      if (await imagesDir.exists()) {
        final imageFiles = await imagesDir
            .list()
            .where((entity) => entity is File && entity.path.endsWith('.jpg'))
            .cast<File>()
            .toList();
        files.addAll(imageFiles);
      }

      return files;
    } catch (e) {
      print('Erreur lors de la récupération des fichiers: $e');
      return [];
    }
  }

  /// Supprime tous les fichiers exportés
  Future<bool> clearExportedData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();

      // Supprimer le fichier CSV
      final csvFile = File('${directory.path}/$_csvFileName');
      if (await csvFile.exists()) {
        await csvFile.delete();
      }

      // Supprimer le dossier d'images
      final imagesDir = Directory('${directory.path}/$_imagesFolder');
      if (await imagesDir.exists()) {
        await imagesDir.delete(recursive: true);
      }

      print('Données exportées supprimées');
      return true;
    } catch (e) {
      print('Erreur lors de la suppression des données: $e');
      return false;
    }
  }

  /// Obtient les statistiques d'export
  Future<ExportStats> getExportStats() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      
      // Taille du fichier CSV
      int csvSize = 0;
      final csvFile = File('${directory.path}/$_csvFileName');
      if (await csvFile.exists()) {
        csvSize = await csvFile.length();
      }

      // Nombre et taille des images
      int imageCount = 0;
      int totalImageSize = 0;
      final imagesDir = Directory('${directory.path}/$_imagesFolder');
      if (await imagesDir.exists()) {
        final imageFiles = await imagesDir
            .list()
            .where((entity) => entity is File && entity.path.endsWith('.jpg'))
            .cast<File>()
            .toList();
        
        imageCount = imageFiles.length;
        for (final file in imageFiles) {
          totalImageSize += await file.length();
        }
      }

      return ExportStats(
        csvSize: csvSize,
        imageCount: imageCount,
        totalImageSize: totalImageSize,
        totalSize: csvSize + totalImageSize,
      );
    } catch (e) {
      print('Erreur lors du calcul des statistiques: $e');
      return ExportStats(
        csvSize: 0,
        imageCount: 0,
        totalImageSize: 0,
        totalSize: 0,
      );
    }
  }

  /// Prépare les données pour l'export CSV
  List<List<String>> _prepareCSVData(List<OverspeedEvent> events) {
    final data = <List<String>>[];

    // En-têtes
    data.add([
      'Date',
      'Heure',
      'ID Véhicule',
      'Vitesse Mesurée (km/h)',
      'Limite Autorisée (km/h)',
      'Dépassement (km/h)',
      'Plaque',
      'Confiance Plaque (%)',
      'Latitude',
      'Longitude',
      'Fichier Image',
      'Conditions Météo',
      'Visibilité',
      'Température (°C)',
    ]);

    // Données
    for (final event in events) {
      data.add([
        _formatDate(event.timestamp),
        _formatTime(event.timestamp),
        event.vehicleId,
        event.measuredSpeed.toStringAsFixed(1),
        event.speedLimit.toStringAsFixed(1),
        (event.measuredSpeed - event.speedLimit).toStringAsFixed(1),
        event.licensePlate ?? 'Non reconnue',
        event.plateConfidence?.toStringAsFixed(1) ?? '0.0',
        event.location?.latitude.toStringAsFixed(6) ?? '',
        event.location?.longitude.toStringAsFixed(6) ?? '',
        event.imagePath ?? '',
        event.weatherConditions ?? '',
        event.visibility ?? '',
        event.temperature?.toStringAsFixed(1) ?? '',
      ]);
    }

    return data;
  }

  /// Formate une date
  String _formatDate(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
           '${dateTime.month.toString().padLeft(2, '0')}/'
           '${dateTime.year}';
  }

  /// Formate une heure
  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// Demande les permissions de stockage
  Future<bool> _requestStoragePermission() async {
    try {
      final status = await Permission.storage.request();
      return status.isGranted;
    } catch (e) {
      print('Erreur lors de la demande de permission: $e');
      return false;
    }
  }
}

/// Statistiques d'export
class ExportStats {
  final int csvSize;
  final int imageCount;
  final int totalImageSize;
  final int totalSize;

  const ExportStats({
    required this.csvSize,
    required this.imageCount,
    required this.totalImageSize,
    required this.totalSize,
  });

  /// Formate la taille en format lisible
  String get formattedTotalSize => _formatBytes(totalSize);
  String get formattedCsvSize => _formatBytes(csvSize);
  String get formattedImageSize => _formatBytes(totalImageSize);

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
