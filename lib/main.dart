import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/app_localizations.dart';
import 'presentation/bloc/radar_bloc.dart';
import 'presentation/bloc/language_bloc.dart';
import 'presentation/screens/radar_screen.dart';
import 'core/services/language_service.dart';

void main() {
  runApp(const RadarSpeedDetectorApp());
}

class RadarSpeedDetectorApp extends StatelessWidget {
  const RadarSpeedDetectorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => LanguageBloc()..add(LoadLanguage())),
        BlocProvider(create: (context) => RadarBloc()),
      ],
      child: BlocBuilder<LanguageBloc, LanguageState>(
        builder: (context, languageState) {
          Locale locale = const Locale('en');

          if (languageState is LanguageLoaded) {
            locale = languageState.locale;
          }

          return MaterialApp(
            title: 'Radar Speed Detector',
            debugShowCheckedModeBanner: false,
            locale: locale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: LanguageService.supportedLanguages
                .map((lang) => lang.locale)
                .toList(),
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness: Brightness.dark,
              ),
              useMaterial3: true,
              scaffoldBackgroundColor: Colors.black,
              appBarTheme: const AppBarTheme(
                backgroundColor: Colors.black,
                foregroundColor: Colors.white,
              ),
              cardTheme: CardThemeData(
                color: Colors.grey[900],
                elevation: 4,
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            home: const RadarScreen(),
          );
        },
      ),
    );
  }
}


