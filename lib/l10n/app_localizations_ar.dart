// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'كاشف سرعة الرادار';

  @override
  String get radarSpeedDetector => 'كاشف سرعة الرادار';

  @override
  String get active => 'نشط';

  @override
  String get stopped => 'متوقف';

  @override
  String get error => 'خطأ';

  @override
  String get initializing => 'تهيئة';

  @override
  String get initializingRadar => 'تهيئة الرادار...';

  @override
  String get start => 'بدء';

  @override
  String get stop => 'إيقاف';

  @override
  String get settings => 'الإعدادات';

  @override
  String get export => 'تصدير';

  @override
  String get speedLimit => 'حد السرعة';

  @override
  String get tolerance => 'التسامح';

  @override
  String get autoSave => 'حفظ تلقائي';

  @override
  String get blurFaces => 'طمس الوجوه';

  @override
  String get blurPlates => 'طمس اللوحات';

  @override
  String get statistics => 'الإحصائيات';

  @override
  String get vehiclesDetected => 'المركبات المكتشفة:';

  @override
  String get completedMeasurements => 'القياسات المكتملة:';

  @override
  String get averageSpeed => 'متوسط السرعة:';

  @override
  String get currentStates => 'الحالات الحالية:';

  @override
  String get beforeA => 'قبل أ';

  @override
  String get betweenAB => 'بين أ-ب';

  @override
  String get completed => 'مكتمل';

  @override
  String get performance => 'الأداء:';

  @override
  String get fps => 'إطار/ثانية:';

  @override
  String get validPixels => 'البكسل الصالحة:';

  @override
  String get portiqueA20m => 'البوابة أ - 20م';

  @override
  String get portiqueB10m => 'البوابة ب - 10م';

  @override
  String get measurementZone => 'منطقة القياس\n10 أمتار';

  @override
  String get overspeedDetected => 'تم اكتشاف تجاوز السرعة';

  @override
  String get photoNotAvailable => 'الصورة غير متوفرة';

  @override
  String get measuredSpeed => 'السرعة المقاسة:';

  @override
  String get authorizedLimit => 'الحد المسموح:';

  @override
  String get overspeed => 'تجاوز:';

  @override
  String get plateNotRecognized => 'اللوحة غير معروفة';

  @override
  String detectedOn(String date) {
    return 'تم الاكتشاف في $date';
  }

  @override
  String get save => 'حفظ';

  @override
  String get share => 'مشاركة';

  @override
  String get close => 'إغلاق';

  @override
  String get recordSaved => 'تم حفظ السجل';

  @override
  String get shareFeatureToImplement => 'ميزة المشاركة قيد التطوير';

  @override
  String get settingsDialog => 'الإعدادات';

  @override
  String get settingsDialogToImplement => 'حوار الإعدادات قيد التطوير';

  @override
  String get exportDataToImplement => 'تصدير البيانات قيد التطوير';

  @override
  String get language => 'اللغة';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get spanish => 'Español';

  @override
  String get german => 'Deutsch';

  @override
  String get italian => 'Italiano';

  @override
  String get portuguese => 'Português';

  @override
  String get arabic => 'العربية';

  @override
  String get chinese => '中文';

  @override
  String get japanese => '日本語';

  @override
  String get russian => 'Русский';

  @override
  String get kmh => 'كم/س';

  @override
  String get meters => 'م';

  @override
  String get confidence => 'الثقة:';

  @override
  String get depth => 'العمق: غير متوفر';

  @override
  String get vehicleId => 'المعرف:';

  @override
  String initializationError(String error) {
    return 'خطأ في التهيئة: $error';
  }

  @override
  String startupError(String error) {
    return 'خطأ في البدء: $error';
  }

  @override
  String stopError(String error) {
    return 'خطأ في الإيقاف: $error';
  }
}
