// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'Detector de Velocidad Radar';

  @override
  String get radarSpeedDetector => 'Detector de Velocidad Radar';

  @override
  String get active => 'ACTIVO';

  @override
  String get stopped => 'DETENIDO';

  @override
  String get error => 'ERROR';

  @override
  String get initializing => 'INIT';

  @override
  String get initializingRadar => 'Inicializando radar...';

  @override
  String get start => 'Iniciar';

  @override
  String get stop => 'Detener';

  @override
  String get settings => 'Configuración';

  @override
  String get export => 'Exportar';

  @override
  String get speedLimit => 'Límite de velocidad';

  @override
  String get tolerance => 'Tolerancia';

  @override
  String get autoSave => 'Guardado automático';

  @override
  String get blurFaces => 'Difuminar caras';

  @override
  String get blurPlates => 'Difuminar placas';

  @override
  String get statistics => 'Estadísticas';

  @override
  String get vehiclesDetected => 'Vehículos detectados:';

  @override
  String get completedMeasurements => 'Mediciones completadas:';

  @override
  String get averageSpeed => 'Velocidad promedio:';

  @override
  String get currentStates => 'Estados actuales:';

  @override
  String get beforeA => 'Antes de A';

  @override
  String get betweenAB => 'Entre A-B';

  @override
  String get completed => 'Completados';

  @override
  String get performance => 'Rendimiento:';

  @override
  String get fps => 'FPS:';

  @override
  String get validPixels => 'Píxeles válidos:';

  @override
  String get portiqueA20m => 'PUERTA A - 20m';

  @override
  String get portiqueB10m => 'PUERTA B - 10m';

  @override
  String get measurementZone => 'ZONA DE MEDICIÓN\n10 metros';

  @override
  String get overspeedDetected => 'EXCESO DE VELOCIDAD DETECTADO';

  @override
  String get photoNotAvailable => 'Foto no disponible';

  @override
  String get measuredSpeed => 'Velocidad medida:';

  @override
  String get authorizedLimit => 'Límite autorizado:';

  @override
  String get overspeed => 'Exceso:';

  @override
  String get plateNotRecognized => 'Placa no reconocida';

  @override
  String detectedOn(String date) {
    return 'Detectado el $date';
  }

  @override
  String get save => 'Guardar';

  @override
  String get share => 'Compartir';

  @override
  String get close => 'Cerrar';

  @override
  String get recordSaved => 'Registro guardado';

  @override
  String get shareFeatureToImplement => 'Función de compartir por implementar';

  @override
  String get settingsDialog => 'Configuración';

  @override
  String get settingsDialogToImplement =>
      'Diálogo de configuración por implementar';

  @override
  String get exportDataToImplement => 'Exportación de datos por implementar';

  @override
  String get language => 'Idioma';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get spanish => 'Español';

  @override
  String get german => 'Deutsch';

  @override
  String get italian => 'Italiano';

  @override
  String get portuguese => 'Português';

  @override
  String get arabic => 'العربية';

  @override
  String get chinese => '中文';

  @override
  String get japanese => '日本語';

  @override
  String get russian => 'Русский';

  @override
  String get kmh => 'km/h';

  @override
  String get meters => 'm';

  @override
  String get confidence => 'Conf:';

  @override
  String get depth => 'Profundidad: N/A';

  @override
  String get vehicleId => 'ID:';

  @override
  String initializationError(String error) {
    return 'Error de inicialización: $error';
  }

  @override
  String startupError(String error) {
    return 'Error de inicio: $error';
  }

  @override
  String stopError(String error) {
    return 'Error de parada: $error';
  }
}
