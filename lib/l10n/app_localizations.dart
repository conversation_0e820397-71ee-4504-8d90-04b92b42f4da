import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Radar Speed Detector'**
  String get appTitle;

  /// No description provided for @radarSpeedDetector.
  ///
  /// In en, this message translates to:
  /// **'Radar Speed Detector'**
  String get radarSpeedDetector;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'ACTIVE'**
  String get active;

  /// No description provided for @stopped.
  ///
  /// In en, this message translates to:
  /// **'STOPPED'**
  String get stopped;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'ERROR'**
  String get error;

  /// No description provided for @initializing.
  ///
  /// In en, this message translates to:
  /// **'INIT'**
  String get initializing;

  /// No description provided for @initializingRadar.
  ///
  /// In en, this message translates to:
  /// **'Initializing radar...'**
  String get initializingRadar;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @stop.
  ///
  /// In en, this message translates to:
  /// **'Stop'**
  String get stop;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @export.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// No description provided for @speedLimit.
  ///
  /// In en, this message translates to:
  /// **'Speed Limit'**
  String get speedLimit;

  /// No description provided for @tolerance.
  ///
  /// In en, this message translates to:
  /// **'Tolerance'**
  String get tolerance;

  /// No description provided for @autoSave.
  ///
  /// In en, this message translates to:
  /// **'Auto-save'**
  String get autoSave;

  /// No description provided for @blurFaces.
  ///
  /// In en, this message translates to:
  /// **'Blur faces'**
  String get blurFaces;

  /// No description provided for @blurPlates.
  ///
  /// In en, this message translates to:
  /// **'Blur plates'**
  String get blurPlates;

  /// No description provided for @statistics.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statistics;

  /// No description provided for @vehiclesDetected.
  ///
  /// In en, this message translates to:
  /// **'Vehicles detected:'**
  String get vehiclesDetected;

  /// No description provided for @completedMeasurements.
  ///
  /// In en, this message translates to:
  /// **'Completed measurements:'**
  String get completedMeasurements;

  /// No description provided for @averageSpeed.
  ///
  /// In en, this message translates to:
  /// **'Average speed:'**
  String get averageSpeed;

  /// No description provided for @currentStates.
  ///
  /// In en, this message translates to:
  /// **'Current states:'**
  String get currentStates;

  /// No description provided for @beforeA.
  ///
  /// In en, this message translates to:
  /// **'Before A'**
  String get beforeA;

  /// No description provided for @betweenAB.
  ///
  /// In en, this message translates to:
  /// **'Between A-B'**
  String get betweenAB;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @performance.
  ///
  /// In en, this message translates to:
  /// **'Performance:'**
  String get performance;

  /// No description provided for @fps.
  ///
  /// In en, this message translates to:
  /// **'FPS:'**
  String get fps;

  /// No description provided for @validPixels.
  ///
  /// In en, this message translates to:
  /// **'Valid pixels:'**
  String get validPixels;

  /// No description provided for @portiqueA20m.
  ///
  /// In en, this message translates to:
  /// **'GATE A - 20m'**
  String get portiqueA20m;

  /// No description provided for @portiqueB10m.
  ///
  /// In en, this message translates to:
  /// **'GATE B - 10m'**
  String get portiqueB10m;

  /// No description provided for @measurementZone.
  ///
  /// In en, this message translates to:
  /// **'MEASUREMENT ZONE\n10 meters'**
  String get measurementZone;

  /// No description provided for @overspeedDetected.
  ///
  /// In en, this message translates to:
  /// **'OVERSPEED DETECTED'**
  String get overspeedDetected;

  /// No description provided for @photoNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'Photo not available'**
  String get photoNotAvailable;

  /// No description provided for @measuredSpeed.
  ///
  /// In en, this message translates to:
  /// **'Measured speed:'**
  String get measuredSpeed;

  /// No description provided for @authorizedLimit.
  ///
  /// In en, this message translates to:
  /// **'Authorized limit:'**
  String get authorizedLimit;

  /// No description provided for @overspeed.
  ///
  /// In en, this message translates to:
  /// **'Overspeed:'**
  String get overspeed;

  /// No description provided for @plateNotRecognized.
  ///
  /// In en, this message translates to:
  /// **'Plate not recognized'**
  String get plateNotRecognized;

  /// Date when overspeed was detected
  ///
  /// In en, this message translates to:
  /// **'Detected on {date}'**
  String detectedOn(String date);

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @recordSaved.
  ///
  /// In en, this message translates to:
  /// **'Record saved'**
  String get recordSaved;

  /// No description provided for @shareFeatureToImplement.
  ///
  /// In en, this message translates to:
  /// **'Share feature to implement'**
  String get shareFeatureToImplement;

  /// No description provided for @settingsDialog.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsDialog;

  /// No description provided for @settingsDialogToImplement.
  ///
  /// In en, this message translates to:
  /// **'Settings dialog to implement'**
  String get settingsDialogToImplement;

  /// No description provided for @exportDataToImplement.
  ///
  /// In en, this message translates to:
  /// **'Data export to implement'**
  String get exportDataToImplement;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @french.
  ///
  /// In en, this message translates to:
  /// **'Français'**
  String get french;

  /// No description provided for @spanish.
  ///
  /// In en, this message translates to:
  /// **'Español'**
  String get spanish;

  /// No description provided for @german.
  ///
  /// In en, this message translates to:
  /// **'Deutsch'**
  String get german;

  /// No description provided for @italian.
  ///
  /// In en, this message translates to:
  /// **'Italiano'**
  String get italian;

  /// No description provided for @portuguese.
  ///
  /// In en, this message translates to:
  /// **'Português'**
  String get portuguese;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// No description provided for @chinese.
  ///
  /// In en, this message translates to:
  /// **'中文'**
  String get chinese;

  /// No description provided for @japanese.
  ///
  /// In en, this message translates to:
  /// **'日本語'**
  String get japanese;

  /// No description provided for @russian.
  ///
  /// In en, this message translates to:
  /// **'Русский'**
  String get russian;

  /// No description provided for @kmh.
  ///
  /// In en, this message translates to:
  /// **'km/h'**
  String get kmh;

  /// No description provided for @meters.
  ///
  /// In en, this message translates to:
  /// **'m'**
  String get meters;

  /// No description provided for @confidence.
  ///
  /// In en, this message translates to:
  /// **'Conf:'**
  String get confidence;

  /// No description provided for @depth.
  ///
  /// In en, this message translates to:
  /// **'Depth: N/A'**
  String get depth;

  /// No description provided for @vehicleId.
  ///
  /// In en, this message translates to:
  /// **'ID:'**
  String get vehicleId;

  /// No description provided for @initializationError.
  ///
  /// In en, this message translates to:
  /// **'Initialization error: {error}'**
  String initializationError(String error);

  /// No description provided for @startupError.
  ///
  /// In en, this message translates to:
  /// **'Startup error: {error}'**
  String startupError(String error);

  /// No description provided for @stopError.
  ///
  /// In en, this message translates to:
  /// **'Stop error: {error}'**
  String stopError(String error);
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'de', 'en', 'es', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
