// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Radar de Vitesse';

  @override
  String get radarSpeedDetector => 'Radar de Vitesse';

  @override
  String get active => 'ACTIF';

  @override
  String get stopped => 'ARRÊTÉ';

  @override
  String get error => 'ERREUR';

  @override
  String get initializing => 'INIT';

  @override
  String get initializingRadar => 'Initialisation du radar...';

  @override
  String get start => 'Démarrer';

  @override
  String get stop => 'Arrêter';

  @override
  String get settings => 'Paramètres';

  @override
  String get export => 'Export';

  @override
  String get speedLimit => 'Limite de vitesse';

  @override
  String get tolerance => 'Tolérance';

  @override
  String get autoSave => 'Sauvegarde auto';

  @override
  String get blurFaces => 'Flou visages';

  @override
  String get blurPlates => 'Flou plaques';

  @override
  String get statistics => 'Statistiques';

  @override
  String get vehiclesDetected => 'Véhicules détectés:';

  @override
  String get completedMeasurements => 'Mesures complétées:';

  @override
  String get averageSpeed => 'Vitesse moyenne:';

  @override
  String get currentStates => 'États actuels:';

  @override
  String get beforeA => 'Avant A';

  @override
  String get betweenAB => 'Entre A-B';

  @override
  String get completed => 'Terminés';

  @override
  String get performance => 'Performance:';

  @override
  String get fps => 'FPS:';

  @override
  String get validPixels => 'Pixels valides:';

  @override
  String get portiqueA20m => 'PORTIQUE A - 20m';

  @override
  String get portiqueB10m => 'PORTIQUE B - 10m';

  @override
  String get measurementZone => 'ZONE DE MESURE\n10 mètres';

  @override
  String get overspeedDetected => 'DÉPASSEMENT DE VITESSE DÉTECTÉ';

  @override
  String get photoNotAvailable => 'Photo non disponible';

  @override
  String get measuredSpeed => 'Vitesse mesurée:';

  @override
  String get authorizedLimit => 'Limite autorisée:';

  @override
  String get overspeed => 'Dépassement:';

  @override
  String get plateNotRecognized => 'Plaque non reconnue';

  @override
  String detectedOn(String date) {
    return 'Détecté le $date';
  }

  @override
  String get save => 'Enregistrer';

  @override
  String get share => 'Partager';

  @override
  String get close => 'Fermer';

  @override
  String get recordSaved => 'Enregistrement sauvegardé';

  @override
  String get shareFeatureToImplement =>
      'Fonctionnalité de partage à implémenter';

  @override
  String get settingsDialog => 'Paramètres';

  @override
  String get settingsDialogToImplement => 'Dialog des paramètres à implémenter';

  @override
  String get exportDataToImplement => 'Export des données à implémenter';

  @override
  String get language => 'Langue';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get spanish => 'Español';

  @override
  String get german => 'Deutsch';

  @override
  String get italian => 'Italiano';

  @override
  String get portuguese => 'Português';

  @override
  String get arabic => 'العربية';

  @override
  String get chinese => '中文';

  @override
  String get japanese => '日本語';

  @override
  String get russian => 'Русский';

  @override
  String get kmh => 'km/h';

  @override
  String get meters => 'm';

  @override
  String get confidence => 'Conf:';

  @override
  String get depth => 'Profondeur: N/A';

  @override
  String get vehicleId => 'ID:';

  @override
  String initializationError(String error) {
    return 'Erreur d\'initialisation: $error';
  }

  @override
  String startupError(String error) {
    return 'Erreur de démarrage: $error';
  }

  @override
  String stopError(String error) {
    return 'Erreur d\'arrêt: $error';
  }
}
