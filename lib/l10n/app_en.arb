{"@@locale": "en", "appTitle": "Radar Speed Detector", "@appTitle": {"description": "The title of the application"}, "radarSpeedDetector": "Radar Speed Detector", "active": "ACTIVE", "stopped": "STOPPED", "error": "ERROR", "initializing": "INIT", "initializingRadar": "Initializing radar...", "start": "Start", "stop": "Stop", "settings": "Settings", "export": "Export", "speedLimit": "Speed Limit", "tolerance": "Tolerance", "autoSave": "Auto-save", "blurFaces": "Blur faces", "blurPlates": "Blur plates", "statistics": "Statistics", "vehiclesDetected": "Vehicles detected:", "completedMeasurements": "Completed measurements:", "averageSpeed": "Average speed:", "currentStates": "Current states:", "beforeA": "Before A", "betweenAB": "Between A-B", "completed": "Completed", "performance": "Performance:", "fps": "FPS:", "validPixels": "Valid pixels:", "portiqueA20m": "GATE A - 20m", "portiqueB10m": "GATE B - 10m", "measurementZone": "MEASUREMENT ZONE\n10 meters", "overspeedDetected": "OVERSPEED DETECTED", "photoNotAvailable": "Photo not available", "measuredSpeed": "Measured speed:", "authorizedLimit": "Authorized limit:", "overspeed": "Overspeed:", "plateNotRecognized": "Plate not recognized", "detectedOn": "Detected on {date}", "@detectedOn": {"description": "Date when overspeed was detected", "placeholders": {"date": {"type": "String"}}}, "save": "Save", "share": "Share", "close": "Close", "recordSaved": "Record saved", "shareFeatureToImplement": "Share feature to implement", "settingsDialog": "Settings", "settingsDialogToImplement": "Settings dialog to implement", "exportDataToImplement": "Data export to implement", "language": "Language", "english": "English", "french": "Français", "spanish": "Español", "german": "De<PERSON>ch", "italian": "Italiano", "portuguese": "Português", "arabic": "العربية", "chinese": "中文", "japanese": "日本語", "russian": "Русский", "kmh": "km/h", "meters": "m", "confidence": "Conf:", "depth": "Depth: N/A", "vehicleId": "ID:", "initializationError": "Initialization error: {error}", "@initializationError": {"placeholders": {"error": {"type": "String"}}}, "startupError": "Startup error: {error}", "@startupError": {"placeholders": {"error": {"type": "String"}}}, "stopError": "Stop error: {error}", "@stopError": {"placeholders": {"error": {"type": "String"}}}}