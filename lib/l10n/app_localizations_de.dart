// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'Radar Geschwindigkeitsmesser';

  @override
  String get radarSpeedDetector => 'Radar Geschwindigkeitsmesser';

  @override
  String get active => 'AKTIV';

  @override
  String get stopped => 'GESTOPPT';

  @override
  String get error => 'FEHLER';

  @override
  String get initializing => 'INIT';

  @override
  String get initializingRadar => 'Radar wird initialisiert...';

  @override
  String get start => 'Starten';

  @override
  String get stop => 'Stoppen';

  @override
  String get settings => 'Einstellungen';

  @override
  String get export => 'Exportieren';

  @override
  String get speedLimit => 'Geschwindigkeitsbegrenzung';

  @override
  String get tolerance => 'Toleranz';

  @override
  String get autoSave => 'Automatisch speichern';

  @override
  String get blurFaces => 'Gesichter unscharf machen';

  @override
  String get blurPlates => 'Kennzeichen unscharf machen';

  @override
  String get statistics => 'Statistiken';

  @override
  String get vehiclesDetected => 'Fahrzeuge erkannt:';

  @override
  String get completedMeasurements => 'Abgeschlossene Messungen:';

  @override
  String get averageSpeed => 'Durchschnittsgeschwindigkeit:';

  @override
  String get currentStates => 'Aktuelle Zustände:';

  @override
  String get beforeA => 'Vor A';

  @override
  String get betweenAB => 'Zwischen A-B';

  @override
  String get completed => 'Abgeschlossen';

  @override
  String get performance => 'Leistung:';

  @override
  String get fps => 'FPS:';

  @override
  String get validPixels => 'Gültige Pixel:';

  @override
  String get portiqueA20m => 'TOR A - 20m';

  @override
  String get portiqueB10m => 'TOR B - 10m';

  @override
  String get measurementZone => 'MESSBEREICH\n10 Meter';

  @override
  String get overspeedDetected => 'GESCHWINDIGKEITSÜBERSCHREITUNG ERKANNT';

  @override
  String get photoNotAvailable => 'Foto nicht verfügbar';

  @override
  String get measuredSpeed => 'Gemessene Geschwindigkeit:';

  @override
  String get authorizedLimit => 'Erlaubtes Limit:';

  @override
  String get overspeed => 'Überschreitung:';

  @override
  String get plateNotRecognized => 'Kennzeichen nicht erkannt';

  @override
  String detectedOn(String date) {
    return 'Erkannt am $date';
  }

  @override
  String get save => 'Speichern';

  @override
  String get share => 'Teilen';

  @override
  String get close => 'Schließen';

  @override
  String get recordSaved => 'Datensatz gespeichert';

  @override
  String get shareFeatureToImplement => 'Teilen-Funktion zu implementieren';

  @override
  String get settingsDialog => 'Einstellungen';

  @override
  String get settingsDialogToImplement =>
      'Einstellungsdialog zu implementieren';

  @override
  String get exportDataToImplement => 'Datenexport zu implementieren';

  @override
  String get language => 'Sprache';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get spanish => 'Español';

  @override
  String get german => 'Deutsch';

  @override
  String get italian => 'Italiano';

  @override
  String get portuguese => 'Português';

  @override
  String get arabic => 'العربية';

  @override
  String get chinese => '中文';

  @override
  String get japanese => '日本語';

  @override
  String get russian => 'Русский';

  @override
  String get kmh => 'km/h';

  @override
  String get meters => 'm';

  @override
  String get confidence => 'Vertr:';

  @override
  String get depth => 'Tiefe: N/A';

  @override
  String get vehicleId => 'ID:';

  @override
  String initializationError(String error) {
    return 'Initialisierungsfehler: $error';
  }

  @override
  String startupError(String error) {
    return 'Startfehler: $error';
  }

  @override
  String stopError(String error) {
    return 'Stoppfehler: $error';
  }
}
