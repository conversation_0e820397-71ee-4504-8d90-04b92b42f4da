// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Radar Speed Detector';

  @override
  String get radarSpeedDetector => 'Radar Speed Detector';

  @override
  String get active => 'ACTIVE';

  @override
  String get stopped => 'STOPPED';

  @override
  String get error => 'ERROR';

  @override
  String get initializing => 'INIT';

  @override
  String get initializingRadar => 'Initializing radar...';

  @override
  String get start => 'Start';

  @override
  String get stop => 'Stop';

  @override
  String get settings => 'Settings';

  @override
  String get export => 'Export';

  @override
  String get speedLimit => 'Speed Limit';

  @override
  String get tolerance => 'Tolerance';

  @override
  String get autoSave => 'Auto-save';

  @override
  String get blurFaces => 'Blur faces';

  @override
  String get blurPlates => 'Blur plates';

  @override
  String get statistics => 'Statistics';

  @override
  String get vehiclesDetected => 'Vehicles detected:';

  @override
  String get completedMeasurements => 'Completed measurements:';

  @override
  String get averageSpeed => 'Average speed:';

  @override
  String get currentStates => 'Current states:';

  @override
  String get beforeA => 'Before A';

  @override
  String get betweenAB => 'Between A-B';

  @override
  String get completed => 'Completed';

  @override
  String get performance => 'Performance:';

  @override
  String get fps => 'FPS:';

  @override
  String get validPixels => 'Valid pixels:';

  @override
  String get portiqueA20m => 'GATE A - 20m';

  @override
  String get portiqueB10m => 'GATE B - 10m';

  @override
  String get measurementZone => 'MEASUREMENT ZONE\n10 meters';

  @override
  String get overspeedDetected => 'OVERSPEED DETECTED';

  @override
  String get photoNotAvailable => 'Photo not available';

  @override
  String get measuredSpeed => 'Measured speed:';

  @override
  String get authorizedLimit => 'Authorized limit:';

  @override
  String get overspeed => 'Overspeed:';

  @override
  String get plateNotRecognized => 'Plate not recognized';

  @override
  String detectedOn(String date) {
    return 'Detected on $date';
  }

  @override
  String get save => 'Save';

  @override
  String get share => 'Share';

  @override
  String get close => 'Close';

  @override
  String get recordSaved => 'Record saved';

  @override
  String get shareFeatureToImplement => 'Share feature to implement';

  @override
  String get settingsDialog => 'Settings';

  @override
  String get settingsDialogToImplement => 'Settings dialog to implement';

  @override
  String get exportDataToImplement => 'Data export to implement';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get spanish => 'Español';

  @override
  String get german => 'Deutsch';

  @override
  String get italian => 'Italiano';

  @override
  String get portuguese => 'Português';

  @override
  String get arabic => 'العربية';

  @override
  String get chinese => '中文';

  @override
  String get japanese => '日本語';

  @override
  String get russian => 'Русский';

  @override
  String get kmh => 'km/h';

  @override
  String get meters => 'm';

  @override
  String get confidence => 'Conf:';

  @override
  String get depth => 'Depth: N/A';

  @override
  String get vehicleId => 'ID:';

  @override
  String initializationError(String error) {
    return 'Initialization error: $error';
  }

  @override
  String startupError(String error) {
    return 'Startup error: $error';
  }

  @override
  String stopError(String error) {
    return 'Stop error: $error';
  }
}
