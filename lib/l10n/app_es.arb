{"@@locale": "es", "appTitle": "Detector de Velocidad Radar", "radarSpeedDetector": "Detector de Velocidad Radar", "active": "ACTIVO", "stopped": "DETENIDO", "error": "ERROR", "initializing": "INIT", "initializingRadar": "Inicializando radar...", "start": "Iniciar", "stop": "Detener", "settings": "Configuración", "export": "Exportar", "speedLimit": "Límite de velocidad", "tolerance": "Tolerancia", "autoSave": "Guardado automático", "blurFaces": "Difuminar caras", "blurPlates": "Difuminar placas", "statistics": "Estadísticas", "vehiclesDetected": "<PERSON>eh<PERSON><PERSON>los detectados:", "completedMeasurements": "Mediciones completadas:", "averageSpeed": "Velocidad promedio:", "currentStates": "Estados actuales:", "beforeA": "Antes de A", "betweenAB": "Entre A-B", "completed": "Completados", "performance": "Rendimiento:", "fps": "FPS:", "validPixels": "Píxeles válidos:", "portiqueA20m": "PUERTA A - 20m", "portiqueB10m": "PUERTA B - 10m", "measurementZone": "ZONA DE MEDICIÓN\n10 metros", "overspeedDetected": "EXCESO DE VELOCIDAD DETECTADO", "photoNotAvailable": "Foto no disponible", "measuredSpeed": "Velocidad medida:", "authorizedLimit": "Límite autorizado:", "overspeed": "Exceso:", "plateNotRecognized": "Placa no reconocida", "detectedOn": "Detectado el {date}", "save": "Guardar", "share": "Compartir", "close": "<PERSON><PERSON><PERSON>", "recordSaved": "Registro guardado", "shareFeatureToImplement": "Función de compartir por implementar", "settingsDialog": "Configuración", "settingsDialogToImplement": "Diálogo de configuración por implementar", "exportDataToImplement": "Exportación de datos por implementar", "language": "Idioma", "english": "English", "french": "Français", "spanish": "Español", "german": "De<PERSON>ch", "italian": "Italiano", "portuguese": "Português", "arabic": "العربية", "chinese": "中文", "japanese": "日本語", "russian": "Русский", "kmh": "km/h", "meters": "m", "confidence": "Conf:", "depth": "Profundidad: N/A", "vehicleId": "ID:", "initializationError": "Error de inicialización: {error}", "startupError": "Error de inicio: {error}", "stopError": "Error de parada: {error}"}