{"@@locale": "de", "appTitle": "Radar Geschwindigkeitsmesser", "radarSpeedDetector": "Radar Geschwindigkeitsmesser", "active": "AKTIV", "stopped": "GESTOPPT", "error": "FEHLER", "initializing": "INIT", "initializingRadar": "Radar wird initialisiert...", "start": "Starten", "stop": "Stoppen", "settings": "Einstellungen", "export": "Exportieren", "speedLimit": "Geschwindigkeitsbegrenzung", "tolerance": "To<PERSON>anz", "autoSave": "Automatisch speichern", "blurFaces": "<PERSON><PERSON><PERSON><PERSON> unscharf machen", "blurPlates": "Kennzeichen unscharf machen", "statistics": "Statistiken", "vehiclesDetected": "Fahrzeuge erkannt:", "completedMeasurements": "Abgeschlossene Messungen:", "averageSpeed": "Durchschnittsgeschwindigkeit:", "currentStates": "Aktuelle Zustände:", "beforeA": "Vor A", "betweenAB": "Zwischen A-B", "completed": "Abgeschlossen", "performance": "Leistung:", "fps": "FPS:", "validPixels": "Gültige Pixel:", "portiqueA20m": "TOR A - 20m", "portiqueB10m": "TOR B - 10m", "measurementZone": "MESSBEREICH\n10 Meter", "overspeedDetected": "GESCHWINDIGKEITSÜBERSCHREITUNG ERKANNT", "photoNotAvailable": "Foto nicht verfügbar", "measuredSpeed": "Gemessene Geschwindigkeit:", "authorizedLimit": "Erlaubtes Limit:", "overspeed": "Überschreitung:", "plateNotRecognized": "Kennzeichen nicht erkannt", "detectedOn": "Erkannt am {date}", "save": "Speichern", "share": "Teilen", "close": "Schließen", "recordSaved": "Datensatz gespeichert", "shareFeatureToImplement": "Teilen-Funktion zu implementieren", "settingsDialog": "Einstellungen", "settingsDialogToImplement": "Einstellungsdialog zu implementieren", "exportDataToImplement": "Datenexport zu implementieren", "language": "<PERSON><PERSON><PERSON>", "english": "English", "french": "Français", "spanish": "Español", "german": "De<PERSON>ch", "italian": "Italiano", "portuguese": "Português", "arabic": "العربية", "chinese": "中文", "japanese": "日本語", "russian": "Русский", "kmh": "km/h", "meters": "m", "confidence": "Vertr:", "depth": "Tiefe: N/A", "vehicleId": "ID:", "initializationError": "Initialisierungsfehler: {error}", "startupError": "Startfehler: {error}", "stopError": "Stoppfehler: {error}"}