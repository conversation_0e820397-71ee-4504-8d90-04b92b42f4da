{"@@locale": "fr", "appTitle": "Radar de Vitesse", "radarSpeedDetector": "Radar de Vitesse", "active": "ACTIF", "stopped": "ARRÊTÉ", "error": "ERREUR", "initializing": "INIT", "initializingRadar": "Initialisation du radar...", "start": "<PERSON><PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Paramètres", "export": "Export", "speedLimit": "Limite de vitesse", "tolerance": "Tolérance", "autoSave": "Sauvegarde auto", "blurFaces": "Flou visages", "blurPlates": "Flou plaques", "statistics": "Statistiques", "vehiclesDetected": "Véhicules détectés:", "completedMeasurements": "Mesures complétées:", "averageSpeed": "Vitesse moyenne:", "currentStates": "États actuels:", "beforeA": "Avant A", "betweenAB": "Entre A-B", "completed": "Te<PERSON>in<PERSON>", "performance": "Performance:", "fps": "FPS:", "validPixels": "Pixels valides:", "portiqueA20m": "PORTIQUE A - 20m", "portiqueB10m": "PORTIQUE B - 10m", "measurementZone": "ZONE DE MESURE\n10 mètres", "overspeedDetected": "DÉPASSEMENT DE VITESSE DÉTECTÉ", "photoNotAvailable": "Photo non disponible", "measuredSpeed": "Vitesse mesurée:", "authorizedLimit": "Limite autorisée:", "overspeed": "Dépassement:", "plateNotRecognized": "Plaque non reconnue", "detectedOn": "Détecté le {date}", "save": "Enregistrer", "share": "Partager", "close": "<PERSON><PERSON><PERSON>", "recordSaved": "Enregistrement sauvegardé", "shareFeatureToImplement": "Fonctionnalité de partage à implémenter", "settingsDialog": "Paramètres", "settingsDialogToImplement": "Dialog des paramètres à implémenter", "exportDataToImplement": "Export des données à implémenter", "language": "<PERSON><PERSON>", "english": "English", "french": "Français", "spanish": "Español", "german": "De<PERSON>ch", "italian": "Italiano", "portuguese": "Português", "arabic": "العربية", "chinese": "中文", "japanese": "日本語", "russian": "Русский", "kmh": "km/h", "meters": "m", "confidence": "Conf:", "depth": "Profondeur: N/A", "vehicleId": "ID:", "initializationError": "Erreur d'initialisation: {error}", "startupError": "<PERSON><PERSON><PERSON> de d<PERSON>: {error}", "stopError": "<PERSON><PERSON><PERSON> d'arr<PERSON><PERSON>: {error}"}