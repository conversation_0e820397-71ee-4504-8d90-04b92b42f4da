import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../l10n/app_localizations.dart';
import '../bloc/radar_bloc.dart';
import 'language_selector_widget.dart';

/// Widget de dialogue des paramètres
class SettingsDialogWidget extends StatefulWidget {
  const SettingsDialogWidget({Key? key}) : super(key: key);

  @override
  State<SettingsDialogWidget> createState() => _SettingsDialogWidgetState();
}

class _SettingsDialogWidgetState extends State<SettingsDialogWidget> {
  late double _speedLimit;
  late double _tolerance;
  late bool _autoSave;
  late bool _blurFaces;
  late bool _blurPlates;
  late bool _showLicensePlates;
  late bool _showDrivers;

  @override
  void initState() {
    super.initState();
    final radarState = context.read<RadarBloc>().state;
    if (radarState is RadarRunning) {
      _speedLimit = radarState.settings.speedLimitKmh;
      _tolerance = radarState.settings.toleranceKmh;
      _autoSave = radarState.settings.autoSaveEnabled;
      _blurFaces = radarState.settings.blurFacesEnabled;
      _blurPlates = radarState.settings.blurPlatesEnabled;
      _showLicensePlates = radarState.settings.showLicensePlatesEnabled;
      _showDrivers = radarState.settings.showDriversEnabled;
    } else {
      _speedLimit = 50.0;
      _tolerance = 5.0;
      _autoSave = true;
      _blurFaces = true;
      _blurPlates = false;
      _showLicensePlates = false;
      _showDrivers = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Dialog(
      backgroundColor: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.white, size: 28),
                const SizedBox(width: 12),
                Text(
                  l10n.settingsDialog,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section Langue
                    _buildSectionTitle(l10n.language),
                    Card(
                      color: Colors.grey[800],
                      child: const LanguageSettingWidget(),
                    ),
                    const SizedBox(height: 20),
                    
                    // Section Radar
                    _buildSectionTitle('Radar'),
                    Card(
                      color: Colors.grey[800],
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // Limite de vitesse
                            Row(
                              children: [
                                const Icon(Icons.speed, color: Colors.white),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        l10n.speedLimit,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '${_speedLimit.round()} ${l10n.kmh}',
                                        style: const TextStyle(
                                          color: Colors.white70,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Slider(
                              value: _speedLimit,
                              min: 30,
                              max: 130,
                              divisions: 20,
                              onChanged: (value) {
                                setState(() {
                                  _speedLimit = value;
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Tolérance
                            Row(
                              children: [
                                const Icon(Icons.tune, color: Colors.white),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        l10n.tolerance,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '${_tolerance.round()} ${l10n.kmh}',
                                        style: const TextStyle(
                                          color: Colors.white70,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Slider(
                              value: _tolerance,
                              min: 0,
                              max: 20,
                              divisions: 20,
                              onChanged: (value) {
                                setState(() {
                                  _tolerance = value;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Section Affichage
                    _buildSectionTitle('Affichage'),
                    Card(
                      color: Colors.grey[800],
                      child: Column(
                        children: [
                          SwitchListTile(
                            title: const Text(
                              'Afficher les plaques',
                              style: TextStyle(color: Colors.white),
                            ),
                            subtitle: Text(
                              'Détecter et afficher les numéros de plaque',
                              style: TextStyle(color: Colors.grey[400]),
                            ),
                            value: _showLicensePlates,
                            onChanged: (value) {
                              setState(() {
                                _showLicensePlates = value;
                              });
                            },
                            secondary: const Icon(Icons.credit_card, color: Colors.white),
                          ),
                          const Divider(color: Colors.grey),
                          SwitchListTile(
                            title: const Text(
                              'Afficher les conducteurs',
                              style: TextStyle(color: Colors.white),
                            ),
                            subtitle: Text(
                              'Détecter et afficher les occupants des véhicules',
                              style: TextStyle(color: Colors.grey[400]),
                            ),
                            value: _showDrivers,
                            onChanged: (value) {
                              setState(() {
                                _showDrivers = value;
                              });
                            },
                            secondary: const Icon(Icons.person, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Section Confidentialité
                    _buildSectionTitle('Privacy'),
                    Card(
                      color: Colors.grey[800],
                      child: Column(
                        children: [
                          SwitchListTile(
                            title: Text(
                              l10n.autoSave,
                              style: const TextStyle(color: Colors.white),
                            ),
                            subtitle: Text(
                              'Automatically save overspeed records',
                              style: TextStyle(color: Colors.grey[400]),
                            ),
                            value: _autoSave,
                            onChanged: (value) {
                              setState(() {
                                _autoSave = value;
                              });
                            },
                            secondary: const Icon(Icons.save, color: Colors.white),
                          ),
                          const Divider(color: Colors.grey),
                          SwitchListTile(
                            title: Text(
                              l10n.blurFaces,
                              style: const TextStyle(color: Colors.white),
                            ),
                            subtitle: Text(
                              'Blur faces in captured photos',
                              style: TextStyle(color: Colors.grey[400]),
                            ),
                            value: _blurFaces,
                            onChanged: (value) {
                              setState(() {
                                _blurFaces = value;
                              });
                            },
                            secondary: const Icon(Icons.face, color: Colors.white),
                          ),
                          const Divider(color: Colors.grey),
                          SwitchListTile(
                            title: Text(
                              l10n.blurPlates,
                              style: const TextStyle(color: Colors.white),
                            ),
                            subtitle: Text(
                              'Blur license plates in captured photos',
                              style: TextStyle(color: Colors.grey[400]),
                            ),
                            value: _blurPlates,
                            onChanged: (value) {
                              setState(() {
                                _blurPlates = value;
                              });
                            },
                            secondary: const Icon(Icons.credit_card, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Actions
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.grey[400]),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _saveSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(l10n.save),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _saveSettings() {
    // Sauvegarder les paramètres via le BLoC
    context.read<RadarBloc>().add(UpdateSettings(
      speedLimit: _speedLimit,
      tolerance: _tolerance,
      autoSave: _autoSave,
      blurFaces: _blurFaces,
      blurPlates: _blurPlates,
      showLicensePlates: _showLicensePlates,
      showDrivers: _showDrivers,
    ));
    
    Navigator.of(context).pop();
  }
}
