import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';

/// Widget d'overlay pour afficher les portiques de mesure
class PortiqueOverlayWidget extends StatelessWidget {
  const PortiqueOverlayWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: <PERSON><PERSON>Painter(),
    );
  }
}

class PortiquePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Calculer les positions des portiques sur l'écran EN PROFONDEUR
    // Les portiques sont placés en profondeur (axe Z), pas verticalement !
    // Plus loin = plus petit et plus haut sur l'écran (perspective)

    final double screenWidth = size.width;
    final double screenHeight = size.height;

    // Portique A (20m) - PLUS LOIN = plus haut et plus petit (perspective)
    final double portiqueAY = screenHeight * 0.25; // Plus haut car plus loin
    final double portiqueAWidth = screenWidth * 0.6; // Plus petit car plus loin

    // Portique B (10m) - PLUS PROCHE = plus bas et plus grand (perspective)
    final double portiqueBY = screenHeight * 0.6; // Plus bas car plus proche
    final double portiqueBWidth = screenWidth * 0.9; // Plus grand car plus proche
    
    // Dessiner le portique A (plus loin, plus petit)
    _drawPortique(
      canvas,
      portiqueAWidth,
      portiqueAY,
      Colors.red,
      'PORTIQUE A - 20m (LOIN)',
      paint,
      (screenWidth - portiqueAWidth) / 2, // Centré
    );

    // Dessiner le portique B (plus proche, plus grand)
    _drawPortique(
      canvas,
      portiqueBWidth,
      portiqueBY,
      Colors.blue,
      'PORTIQUE B - 10m (PROCHE)',
      paint,
      (screenWidth - portiqueBWidth) / 2, // Centré
    );
    
    // Dessiner la zone de mesure (en perspective)
    _drawMeasurementZone(
      canvas,
      screenWidth,
      portiqueAY,
      portiqueBY,
      portiqueAWidth,
      portiqueBWidth,
      paint,
    );
  }

  void _drawPortique(
    Canvas canvas,
    double portiqueWidth,
    double y,
    Color color,
    String label,
    Paint paint,
    double offsetX,
  ) {
    paint.color = color;

    // Ligne principale du portique (en perspective)
    canvas.drawLine(
      Offset(offsetX, y),
      Offset(offsetX + portiqueWidth, y),
      paint,
    );
    
    // Marqueurs sur les côtés (en perspective)
    const double markerHeight = 20;
    canvas.drawLine(
      Offset(offsetX, y - markerHeight / 2),
      Offset(offsetX, y + markerHeight / 2),
      paint,
    );
    canvas.drawLine(
      Offset(offsetX + portiqueWidth, y - markerHeight / 2),
      Offset(offsetX + portiqueWidth, y + markerHeight / 2),
      paint,
    );
    
    // Label du portique
    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: TextStyle(
          color: color,
          fontSize: 14,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.8),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        offsetX + (portiqueWidth - textPainter.width) / 2,
        y - textPainter.height - 8,
      ),
    );
  }

  void _drawMeasurementZone(
    Canvas canvas,
    double screenWidth,
    double portiqueAY,
    double portiqueBY,
    double portiqueAWidth,
    double portiqueBWidth,
    Paint paint,
  ) {
    // Zone de mesure entre les portiques (trapèze en perspective)
    paint.color = Colors.yellow.withOpacity(0.2);
    paint.style = PaintingStyle.fill;

    // Créer un trapèze pour représenter la perspective
    final path = Path();
    final double offsetAX = (screenWidth - portiqueAWidth) / 2;
    final double offsetBX = (screenWidth - portiqueBWidth) / 2;

    path.moveTo(offsetAX, portiqueAY);
    path.lineTo(offsetAX + portiqueAWidth, portiqueAY);
    path.lineTo(offsetBX + portiqueBWidth, portiqueBY);
    path.lineTo(offsetBX, portiqueBY);
    path.close();

    canvas.drawPath(path, paint);
    
    // Bordures de la zone (en perspective)
    paint.color = Colors.yellow;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;

    // Lignes pointillées sur les côtés (perspective)
    final double offsetAX2 = (screenWidth - portiqueAWidth) / 2;
    final double offsetBX2 = (screenWidth - portiqueBWidth) / 2;

    _drawDashedLine(
      canvas,
      Offset(offsetAX2, portiqueAY),
      Offset(offsetBX2, portiqueBY),
      paint,
    );

    _drawDashedLine(
      canvas,
      Offset(offsetAX2 + portiqueAWidth, portiqueAY),
      Offset(offsetBX2 + portiqueBWidth, portiqueBY),
      paint,
    );
    
    // Label de la zone de mesure (centré dans le trapèze)
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'ZONE DE MESURE\n10 mètres EN PROFONDEUR',
        style: TextStyle(
          color: Colors.yellow,
          fontSize: 12,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: Colors.black,
              offset: Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        screenWidth / 2 - textPainter.width / 2,
        (portiqueAY + portiqueBY) / 2 - textPainter.height / 2,
      ),
    );
  }

  void _drawDashedLine(
    Canvas canvas,
    Offset start,
    Offset end,
    Paint paint,
  ) {
    const double dashLength = 5.0;
    const double gapLength = 3.0;
    
    final double totalLength = (end - start).distance;
    final double dashCount = totalLength / (dashLength + gapLength);
    
    for (int i = 0; i < dashCount; i++) {
      final double startRatio = i * (dashLength + gapLength) / totalLength;
      final double endRatio = (i * (dashLength + gapLength) + dashLength) / totalLength;
      
      if (endRatio > 1.0) break;
      
      final Offset dashStart = Offset.lerp(start, end, startRatio)!;
      final Offset dashEnd = Offset.lerp(start, end, endRatio)!;
      
      canvas.drawLine(dashStart, dashEnd, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
