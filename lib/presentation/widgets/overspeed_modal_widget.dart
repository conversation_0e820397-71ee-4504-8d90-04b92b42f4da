import 'package:flutter/material.dart';
import 'dart:io';
import '../../core/models/vehicle_data.dart';

/// Widget modal pour afficher les dépassements de vitesse
class OverspeedModalWidget extends StatelessWidget {
  final OverspeedEvent overspeedEvent;
  final VoidCallback onClose;

  const OverspeedModalWidget({
    Key? key,
    required this.overspeedEvent,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black87,
      child: SafeArea(
        child: Column(
          children: [
            // Header avec bouton fermer
            _buildHeader(context),
            
            // Contenu principal
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Photo du véhicule
                    Expanded(
                      flex: 3,
                      child: _buildVehiclePhoto(),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Informations de dépassement
                    Expanded(
                      flex: 2,
                      child: _buildOverspeedInfo(),
                    ),
                  ],
                ),
              ),
            ),
            
            // Boutons d'action
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'DÉPASSEMENT DE VITESSE DÉTECTÉ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: onClose,
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehiclePhoto() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red, width: 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: overspeedEvent.photoPath != null
            ? Image.file(
                File(overspeedEvent.photoPath!),
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPhotoPlaceholder();
                },
              )
            : _buildPhotoPlaceholder(),
      ),
    );
  }

  Widget _buildPhotoPlaceholder() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.camera_alt,
            size: 64,
            color: Colors.white24,
          ),
          SizedBox(height: 8),
          Text(
            'Photo non disponible',
            style: TextStyle(
              color: Colors.white54,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverspeedInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange, width: 1),
      ),
      child: Column(
        children: [
          // Vitesse mesurée
          _buildInfoRow(
            'Vitesse mesurée:',
            '${overspeedEvent.measuredSpeed.toStringAsFixed(1)} km/h',
            Colors.red,
            fontSize: 24,
          ),
          
          const SizedBox(height: 12),
          
          // Limite autorisée
          _buildInfoRow(
            'Limite autorisée:',
            '${overspeedEvent.speedLimit.toStringAsFixed(0)} km/h',
            Colors.orange,
            fontSize: 18,
          ),
          
          const SizedBox(height: 12),
          
          // Dépassement
          _buildInfoRow(
            'Dépassement:',
            '+${(overspeedEvent.measuredSpeed - overspeedEvent.speedLimit).toStringAsFixed(1)} km/h',
            Colors.red,
            fontSize: 18,
          ),
          
          const SizedBox(height: 16),
          
          // Plaque d'immatriculation
          if (overspeedEvent.licensePlate != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.black, width: 2),
              ),
              child: Text(
                overspeedEvent.licensePlate!,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                ),
              ),
            )
          else
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey, width: 1),
              ),
              child: const Text(
                'Plaque non reconnue',
                style: TextStyle(
                  color: Colors.white54,
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          
          const SizedBox(height: 12),
          
          // Horodatage
          Text(
            'Détecté le ${_formatDateTime(overspeedEvent.timestamp)}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    Color valueColor, {
    double fontSize = 16,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: valueColor,
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Bouton Enregistrer
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _saveRecord(context),
              icon: const Icon(Icons.save),
              label: const Text('Enregistrer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Bouton Partager
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _shareRecord(context),
              icon: const Icon(Icons.share),
              label: const Text('Partager'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Bouton Fermer
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onClose,
              icon: const Icon(Icons.close),
              label: const Text('Fermer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
           '${dateTime.month.toString().padLeft(2, '0')}/'
           '${dateTime.year} à '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  void _saveRecord(BuildContext context) {
    // TODO: Implémenter la sauvegarde
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Enregistrement sauvegardé'),
        backgroundColor: Colors.green,
      ),
    );
    onClose();
  }

  void _shareRecord(BuildContext context) {
    // TODO: Implémenter le partage
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité de partage à implémenter'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
