import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/models/vehicle_data.dart';

/// Widget du panneau de contrôle pour les paramètres rapides
class ControlPanelWidget extends StatelessWidget {
  final UserSettings settings;
  final Function(UserSettings) onSettingsChanged;

  const ControlPanelWidget({
    Key? key,
    required this.settings,
    required this.onSettingsChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      children: [
        // Limite de vitesse
        Row(
          children: [
            const Icon(Icons.speed, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              '${l10n.speedLimit}:',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Slider(
                value: settings.speedLimitKmh,
                min: 30.0,
                max: 130.0,
                divisions: 20,
                activeColor: Colors.blue,
                inactiveColor: Colors.grey,
                onChanged: (value) {
                  onSettingsChanged(settings.copyWith(speedLimitKmh: value));
                },
              ),
            ),
            SizedBox(
              width: 60,
              child: Text(
                '${settings.speedLimitKmh.toInt()} ${l10n.kmh}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Tolérance
        Row(
          children: [
            const Icon(Icons.tune, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              '${l10n.tolerance}:',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Slider(
                value: settings.toleranceKmh,
                min: 0.0,
                max: 15.0,
                divisions: 15,
                activeColor: Colors.orange,
                inactiveColor: Colors.grey,
                onChanged: (value) {
                  onSettingsChanged(settings.copyWith(toleranceKmh: value));
                },
              ),
            ),
            SizedBox(
              width: 60,
              child: Text(
                '+${settings.toleranceKmh.toInt()} km/h',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Options rapides
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Auto-save
            _buildToggleButton(
              icon: Icons.save,
              label: 'Auto-save',
              isActive: settings.autoSaveEnabled,
              onTap: () {
                onSettingsChanged(
                  settings.copyWith(autoSaveEnabled: !settings.autoSaveEnabled),
                );
              },
            ),
            
            // Floutage des visages
            _buildToggleButton(
              icon: Icons.face,
              label: 'Flou visages',
              isActive: settings.blurFacesEnabled,
              onTap: () {
                onSettingsChanged(
                  settings.copyWith(blurFacesEnabled: !settings.blurFacesEnabled),
                );
              },
            ),
            
            // Floutage des plaques
            _buildToggleButton(
              icon: Icons.credit_card,
              label: 'Flou plaques',
              isActive: settings.blurPlatesEnabled,
              onTap: () {
                onSettingsChanged(
                  settings.copyWith(blurPlatesEnabled: !settings.blurPlatesEnabled),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildToggleButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.blue : Colors.grey.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isActive ? Colors.blue : Colors.grey,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
