import 'package:flutter/material.dart';
import '../../core/models/vehicle_data.dart';
import '../../core/services/radar_service.dart';
import '../../core/constants/app_constants.dart';

/// Widget du panneau de statistiques
class StatsPanelWidget extends StatelessWidget {
  final RadarStats stats;
  final List<VehicleData> vehicles;

  const StatsPanelWidget({
    Key? key,
    required this.stats,
    required this.vehicles,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.black87,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Titre
            const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.analytics, color: Colors.white, size: 16),
                SizedBox(width: 6),
                Text(
                  'Statistiques',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // Véhicules détectés
            _buildStatRow(
              'Véhicules détectés:',
              '${stats.totalVehicles}',
              Colors.blue,
            ),
            
            // Mesures complétées
            _buildStatRow(
              'Mesures complétées:',
              '${stats.completedMeasurements}',
              Colors.green,
            ),
            
            // Vitesse moyenne
            if (stats.averageSpeed != null)
              _buildStatRow(
                'Vitesse moyenne:',
                '${stats.averageSpeed!.toStringAsFixed(1)} km/h',
                Colors.orange,
              ),
            
            const SizedBox(height: 8),
            
            // Véhicules par état
            _buildVehicleStateStats(),
            
            const SizedBox(height: 8),
            
            // FPS et performance
            _buildPerformanceStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleStateStats() {
    final Map<VehicleState, int> stateCounts = {};
    
    for (final vehicle in vehicles) {
      stateCounts[vehicle.state] = (stateCounts[vehicle.state] ?? 0) + 1;
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'États actuels:',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 11,
          ),
        ),
        const SizedBox(height: 4),
        
        // Avant portique A
        _buildStateRow(
          'Avant A:',
          '${stateCounts[VehicleState.beforeA] ?? 0}',
          Colors.orange,
        ),
        
        // Entre portiques
        _buildStateRow(
          'Entre A-B:',
          '${stateCounts[VehicleState.betweenAB] ?? 0}',
          Colors.yellow,
        ),
        
        // Terminés
        _buildStateRow(
          'Terminés:',
          '${stateCounts[VehicleState.done] ?? 0}',
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildStateRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Performance:',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 11,
          ),
        ),
        const SizedBox(height: 4),
        
        // FPS (simulé pour l'instant)
        _buildStatRow(
          'FPS:',
          '60', // TODO: Obtenir le FPS réel
          Colors.cyan,
        ),
        
        // Pixels valides (simulé)
        _buildStatRow(
          'Pixels valides:',
          '85%', // TODO: Calculer le pourcentage réel
          Colors.purple,
        ),
        
        // Statut
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: stats.isRunning ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              stats.isRunning ? 'ACTIF' : 'ARRÊTÉ',
              style: TextStyle(
                color: stats.isRunning ? Colors.green : Colors.red,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
