import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../l10n/app_localizations.dart';
import '../bloc/language_bloc.dart';
import '../../core/services/language_service.dart';

/// Widget de sélection de langue
class LanguageSelectorWidget extends StatelessWidget {
  const LanguageSelectorWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        LanguageOption currentLanguage = LanguageService.supportedLanguages.first;
        
        if (state is LanguageLoaded) {
          currentLanguage = state.languageOption;
        }
        
        return PopupMenuButton<LanguageOption>(
          icon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                currentLanguage.flag,
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Text(
                currentLanguage.nativeName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 4),
              const Icon(
                Icons.arrow_drop_down,
                color: Colors.white,
              ),
            ],
          ),
          onSelected: (LanguageOption language) {
            context.read<LanguageBloc>().add(ChangeLanguage(language.code));
          },
          itemBuilder: (BuildContext context) {
            return LanguageService.supportedLanguages.map((language) {
              final isSelected = language.code == currentLanguage.code;
              
              return PopupMenuItem<LanguageOption>(
                value: language,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      Text(
                        language.flag,
                        style: const TextStyle(fontSize: 20),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              language.nativeName,
                              style: TextStyle(
                                fontWeight: isSelected 
                                    ? FontWeight.bold 
                                    : FontWeight.normal,
                                color: isSelected 
                                    ? Theme.of(context).primaryColor 
                                    : null,
                              ),
                            ),
                            if (language.name != language.nativeName)
                              Text(
                                language.name,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          Icons.check,
                          color: Theme.of(context).primaryColor,
                          size: 20,
                        ),
                    ],
                  ),
                ),
              );
            }).toList();
          },
          color: Colors.grey[900],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
      },
    );
  }
}

/// Widget simple de sélection de langue pour les paramètres
class LanguageSettingWidget extends StatelessWidget {
  const LanguageSettingWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        LanguageOption currentLanguage = LanguageService.supportedLanguages.first;
        
        if (state is LanguageLoaded) {
          currentLanguage = state.languageOption;
        }
        
        return ListTile(
          leading: const Icon(Icons.language, color: Colors.white),
          title: Text(
            l10n.language,
            style: const TextStyle(color: Colors.white),
          ),
          subtitle: Text(
            currentLanguage.nativeName,
            style: const TextStyle(color: Colors.white70),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                currentLanguage.flag,
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.arrow_forward_ios, color: Colors.white70, size: 16),
            ],
          ),
          onTap: () => _showLanguageDialog(context, currentLanguage),
        );
      },
    );
  }

  void _showLanguageDialog(BuildContext context, LanguageOption currentLanguage) {
    final l10n = AppLocalizations.of(context)!;
    
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            l10n.language,
            style: const TextStyle(color: Colors.white),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: LanguageService.supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = LanguageService.supportedLanguages[index];
                final isSelected = language.code == currentLanguage.code;
                
                return ListTile(
                  leading: Text(
                    language.flag,
                    style: const TextStyle(fontSize: 20),
                  ),
                  title: Text(
                    language.nativeName,
                    style: TextStyle(
                      color: isSelected ? Theme.of(context).primaryColor : Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  subtitle: language.name != language.nativeName
                      ? Text(
                          language.name,
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                        )
                      : null,
                  trailing: isSelected
                      ? Icon(
                          Icons.check,
                          color: Theme.of(context).primaryColor,
                        )
                      : null,
                  onTap: () {
                    context.read<LanguageBloc>().add(ChangeLanguage(language.code));
                    Navigator.of(dialogContext).pop();
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                l10n.close,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
