import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../core/models/vehicle_data.dart';
import '../../core/constants/app_constants.dart';

/// Widget d'overlay pour afficher les véhicules détectés avec couleurs uniques
class VehicleOverlayWidget extends StatelessWidget {
  final List<VehicleData> vehicles;
  final bool showLicensePlates;
  final bool showDrivers;

  const VehicleOverlayWidget({
    Key? key,
    required this.vehicles,
    this.showLicensePlates = false,
    this.showDrivers = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: VehiclePainter(
        vehicles,
        showLicensePlates: showLicensePlates,
        showDrivers: showDrivers,
      ),
    );
  }
}

class VehiclePainter extends CustomPainter {
  final List<VehicleData> vehicles;
  final bool showLicensePlates;
  final bool showDrivers;

  // Couleurs prédéfinies pour les véhicules
  static final List<Color> _vehicleColors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.cyan,
    Colors.pink,
    Colors.yellow,
    Colors.teal,
    Colors.indigo,
    Colors.lime,
    Colors.amber,
  ];

  VehiclePainter(
    this.vehicles, {
    this.showLicensePlates = false,
    this.showDrivers = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (final vehicle in vehicles) {
      _drawVehicle(canvas, size, vehicle);
    }
  }

  void _drawVehicle(Canvas canvas, Size size, VehicleData vehicle) {
    final bbox = vehicle.bbox;
    
    // Convertir les coordonnées de la bounding box à l'écran
    // Note: Ces coordonnées devraient être normalisées (0-1) depuis la détection
    final double left = bbox.x * size.width;
    final double top = bbox.y * size.height;
    final double right = (bbox.x + bbox.width) * size.width;
    final double bottom = (bbox.y + bbox.height) * size.height;
    
    final rect = Rect.fromLTRB(left, top, right, bottom);
    
    // Couleur selon l'état du véhicule
    Color boxColor = _getVehicleColor(vehicle);
    
    // Dessiner la bounding box
    final paint = Paint()
      ..color = boxColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawRect(rect, paint);
    
    // Dessiner le point de profondeur (bottom-center)
    _drawDepthPoint(canvas, vehicle, size);
    
    // Dessiner les informations du véhicule
    _drawVehicleInfo(canvas, vehicle, rect);
  }

  Color _getVehicleColor(VehicleData vehicle) {
    // Utiliser le code couleur du véhicule s'il existe
    if (vehicle.colorCode != null) {
      return _vehicleColors[vehicle.colorCode! % _vehicleColors.length];
    }

    // Générer une couleur basée sur l'ID du véhicule pour la cohérence
    final hash = vehicle.id.hashCode;
    return _vehicleColors[hash.abs() % _vehicleColors.length];
  }

  void _drawDepthPoint(Canvas canvas, VehicleData vehicle, Size size) {
    final bbox = vehicle.bbox;
    
    // Point bottom-center
    final double centerX = (bbox.x + bbox.width / 2) * size.width;
    final double bottomY = (bbox.y + bbox.height) * size.height;
    
    final paint = Paint()
      ..color = vehicle.depth != null ? Colors.cyan : Colors.red
      ..style = PaintingStyle.fill;
    
    // Cercle pour le point de profondeur
    canvas.drawCircle(
      Offset(centerX, bottomY),
      4.0,
      paint,
    );
    
    // Croix pour marquer le point exact
    final crossPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.0;
    
    canvas.drawLine(
      Offset(centerX - 3, bottomY),
      Offset(centerX + 3, bottomY),
      crossPaint,
    );
    canvas.drawLine(
      Offset(centerX, bottomY - 3),
      Offset(centerX, bottomY + 3),
      crossPaint,
    );
  }

  void _drawVehicleInfo(Canvas canvas, VehicleData vehicle, Rect rect) {
    final List<String> infoLines = [];
    
    // ID du véhicule
    infoLines.add('ID: ${vehicle.id.split('_').last}');
    
    // Profondeur
    if (vehicle.depth != null) {
      infoLines.add('${vehicle.depth!.toStringAsFixed(1)}m');
    } else {
      infoLines.add('Profondeur: N/A');
    }
    
    // Vitesse (affichage proéminent)
    if (vehicle.speedKmh != null) {
      infoLines.add('${vehicle.speedKmh!.toStringAsFixed(1)} km/h');
    }

    // Plaque d'immatriculation (si activée et disponible)
    if (showLicensePlates && vehicle.licensePlate != null) {
      infoLines.add('Plaque: ${vehicle.licensePlate}');
    }

    // Information conducteur (si activée et disponible)
    if (showDrivers && vehicle.driverInfo != null) {
      infoLines.add('Conducteur: ${vehicle.driverInfo}');
    }
    
    // État
    String stateText;
    switch (vehicle.state) {
      case VehicleState.beforeA:
        stateText = 'Avant A';
        break;
      case VehicleState.betweenAB:
        stateText = 'Entre A-B';
        break;
      case VehicleState.done:
        stateText = 'Terminé';
        break;
    }
    infoLines.add(stateText);
    
    // Confiance
    infoLines.add('Conf: ${(vehicle.confidence * 100).toInt()}%');
    
    // Dessiner le fond du texte
    final textPainter = TextPainter(
      text: TextSpan(
        text: infoLines.join('\n'),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    // Position du texte (au-dessus de la bounding box)
    final double textX = rect.left;
    final double textY = rect.top - textPainter.height - 4;
    
    // Fond semi-transparent
    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    final backgroundRect = Rect.fromLTWH(
      textX - 2,
      textY - 2,
      textPainter.width + 4,
      textPainter.height + 4,
    );
    
    canvas.drawRect(backgroundRect, backgroundPaint);
    
    // Texte
    textPainter.paint(canvas, Offset(textX, textY));
  }

  @override
  bool shouldRepaint(covariant VehiclePainter oldDelegate) {
    return oldDelegate.vehicles != vehicles;
  }
}
