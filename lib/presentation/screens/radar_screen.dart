import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:camera/camera.dart';
import '../../l10n/app_localizations.dart';
import '../bloc/radar_bloc.dart';
import '../widgets/camera_preview_widget.dart';
import '../widgets/vehicle_overlay_widget.dart';
import '../widgets/portique_overlay_widget.dart';
import '../widgets/control_panel_widget.dart';
import '../widgets/stats_panel_widget.dart';
import '../widgets/overspeed_modal_widget.dart';
import '../widgets/language_selector_widget.dart';
import '../widgets/settings_dialog_widget.dart';
import '../../core/models/vehicle_data.dart';

class RadarScreen extends StatefulWidget {
  const RadarScreen({Key? key}) : super(key: key);

  @override
  State<RadarScreen> createState() => _RadarScreenState();
}

class _RadarScreenState extends State<RadarScreen> {
  @override
  void initState() {
    super.initState();
    // Initialiser le radar au démarrage
    context.read<RadarBloc>().add(InitializeRadar());
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(l10n.radarSpeedDetector),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          const LanguageSelectorWidget(),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: BlocConsumer<RadarBloc, RadarState>(
        listener: (context, state) {
          if (state is RadarError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              // Aperçu caméra en arrière-plan
              _buildCameraPreview(state),
              
              // Overlays des portiques
              if (state is RadarRunning)
                const PortiqueOverlayWidget(),
              
              // Overlays des véhicules
              if (state is RadarRunning)
                VehicleOverlayWidget(
                  vehicles: state.vehicles,
                  showLicensePlates: state.settings.showLicensePlatesEnabled,
                  showDrivers: state.settings.showDriversEnabled,
                ),
              
              // Panneau de contrôle (en haut)
              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                left: 16,
                right: 16,
                child: _buildControlPanel(state),
              ),
              
              // Panneau de statistiques (en bas à droite)
              if (state is RadarRunning)
                Positioned(
                  bottom: 100,
                  right: 16,
                  child: StatsPanelWidget(
                    stats: state.stats,
                    vehicles: state.vehicles,
                  ),
                ),
              
              // Boutons d'action (en bas)
              Positioned(
                bottom: MediaQuery.of(context).padding.bottom + 16,
                left: 16,
                right: 16,
                child: _buildActionButtons(state),
              ),
              
              // Modal de dépassement
              if (state is OverspeedModalVisible)
                OverspeedModalWidget(
                  overspeedEvent: state.overspeedEvent,
                  onClose: () {
                    // Retourner à l'état précédent
                    context.read<RadarBloc>().emit(state.previousState);
                  },
                ),
              
              // Indicateur de chargement
              if (state is RadarInitializing)
                Container(
                  color: Colors.black54,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: Colors.white),
                        SizedBox(height: 16),
                        Text(
                          'Initialisation du radar...',
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCameraPreview(RadarState state) {
    if (state is RadarRunning || state is OverspeedModalVisible) {
      return const CameraPreviewWidget();
    }
    
    return Container(
      color: Colors.black,
      child: const Center(
        child: Icon(
          Icons.camera_alt,
          size: 100,
          color: Colors.white24,
        ),
      ),
    );
  }

  Widget _buildControlPanel(RadarState state) {
    return Card(
      color: Colors.black87,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                const Icon(Icons.speed, color: Colors.white),
                const SizedBox(width: 8),
                const Text(
                  'Radar de Vitesse',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildStatusIndicator(state),
              ],
            ),
            const SizedBox(height: 16),
            ControlPanelWidget(
              settings: _getSettings(state),
              onSettingsChanged: (settings) {
                context.read<RadarBloc>().add(UpdateSettings(
                  speedLimit: settings.speedLimitKmh,
                  tolerance: settings.toleranceKmh,
                  autoSave: settings.autoSaveEnabled,
                  blurFaces: settings.blurFacesEnabled,
                  blurPlates: settings.blurPlatesEnabled,
                  showLicensePlates: settings.showLicensePlatesEnabled,
                  showDrivers: settings.showDriversEnabled,
                ));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(RadarState state) {
    Color color;
    String text;
    
    if (state is RadarRunning) {
      color = Colors.green;
      text = 'ACTIF';
    } else if (state is RadarInitializing) {
      color = Colors.orange;
      text = 'INIT';
    } else if (state is RadarError) {
      color = Colors.red;
      text = 'ERREUR';
    } else {
      color = Colors.grey;
      text = 'ARRÊTÉ';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildActionButtons(RadarState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Bouton Start/Stop
        ElevatedButton.icon(
          onPressed: _canToggleRadar(state) ? () => _toggleRadar(state) : null,
          icon: Icon(_getToggleIcon(state)),
          label: Text(_getToggleText(state)),
          style: ElevatedButton.styleFrom(
            backgroundColor: _getToggleColor(state),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
        
        // Bouton Paramètres
        ElevatedButton.icon(
          onPressed: () => _showSettingsDialog(context),
          icon: const Icon(Icons.settings),
          label: const Text('Paramètres'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
        
        // Bouton Export
        ElevatedButton.icon(
          onPressed: state is RadarRunning ? () => _exportData(state) : null,
          icon: const Icon(Icons.download),
          label: const Text('Export'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }

  bool _canToggleRadar(RadarState state) {
    return state is RadarInitialized || 
           state is RadarRunning || 
           state is RadarStopped;
  }

  void _toggleRadar(RadarState state) {
    if (state is RadarRunning) {
      context.read<RadarBloc>().add(StopRadar());
    } else {
      context.read<RadarBloc>().add(StartRadar());
    }
  }

  IconData _getToggleIcon(RadarState state) {
    return state is RadarRunning ? Icons.stop : Icons.play_arrow;
  }

  String _getToggleText(RadarState state) {
    return state is RadarRunning ? 'Arrêter' : 'Démarrer';
  }

  Color _getToggleColor(RadarState state) {
    return state is RadarRunning ? Colors.red : Colors.green;
  }

  UserSettings _getSettings(RadarState state) {
    if (state is RadarRunning) return state.settings;
    if (state is RadarStopped) return state.settings;
    if (state is RadarInitialized) return state.settings;
    return const UserSettings();
  }



  void _exportData(RadarRunning state) {
    final l10n = AppLocalizations.of(context)!;
    // TODO: Implémenter l'export des données
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(l10n.exportDataToImplement),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SettingsDialogWidget(),
    );
  }
}
