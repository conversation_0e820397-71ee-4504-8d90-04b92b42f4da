import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../core/models/vehicle_data.dart';
import '../../core/services/radar_service.dart';
import '../../core/services/mock_radar_service.dart';
import '../../core/constants/app_constants.dart';

// Events
abstract class RadarEvent extends Equatable {
  const RadarEvent();
  
  @override
  List<Object?> get props => [];
}

class InitializeRadar extends RadarEvent {}

class StartRadar extends RadarEvent {}

class StopRadar extends RadarEvent {}

class UpdateSettings extends RadarEvent {
  final double speedLimit;
  final double tolerance;
  final bool autoSave;
  final bool blurFaces;
  final bool blurPlates;
  final bool showLicensePlates;
  final bool showDrivers;

  const UpdateSettings({
    required this.speedLimit,
    required this.tolerance,
    required this.autoSave,
    required this.blurFaces,
    required this.blurPlates,
    required this.showLicensePlates,
    required this.showDrivers,
  });

  @override
  List<Object> get props => [speedLimit, tolerance, autoSave, blurFaces, blurPlates, showLicensePlates, showDrivers];
}



class VehiclesUpdated extends RadarEvent {
  final List<VehicleData> vehicles;
  
  const VehiclesUpdated(this.vehicles);
  
  @override
  List<Object> get props => [vehicles];
}

class OverspeedDetected extends RadarEvent {
  final OverspeedEvent overspeedEvent;
  
  const OverspeedDetected(this.overspeedEvent);
  
  @override
  List<Object> get props => [overspeedEvent];
}

// States
abstract class RadarState extends Equatable {
  const RadarState();
  
  @override
  List<Object?> get props => [];
}

class RadarInitial extends RadarState {}

class RadarInitializing extends RadarState {}

class RadarInitialized extends RadarState {
  final UserSettings settings;
  
  const RadarInitialized(this.settings);
  
  @override
  List<Object> get props => [settings];
}

class RadarRunning extends RadarState {
  final List<VehicleData> vehicles;
  final UserSettings settings;
  final RadarStats stats;
  final List<OverspeedEvent> recentOverspeeds;
  
  const RadarRunning({
    required this.vehicles,
    required this.settings,
    required this.stats,
    this.recentOverspeeds = const [],
  });
  
  @override
  List<Object> get props => [vehicles, settings, stats, recentOverspeeds];
  
  RadarRunning copyWith({
    List<VehicleData>? vehicles,
    UserSettings? settings,
    RadarStats? stats,
    List<OverspeedEvent>? recentOverspeeds,
  }) {
    return RadarRunning(
      vehicles: vehicles ?? this.vehicles,
      settings: settings ?? this.settings,
      stats: stats ?? this.stats,
      recentOverspeeds: recentOverspeeds ?? this.recentOverspeeds,
    );
  }
}

class RadarStopped extends RadarState {
  final UserSettings settings;
  
  const RadarStopped(this.settings);
  
  @override
  List<Object> get props => [settings];
}

class RadarError extends RadarState {
  final String message;
  
  const RadarError(this.message);
  
  @override
  List<Object> get props => [message];
}

class OverspeedModalVisible extends RadarState {
  final OverspeedEvent overspeedEvent;
  final RadarRunning previousState;
  
  const OverspeedModalVisible({
    required this.overspeedEvent,
    required this.previousState,
  });
  
  @override
  List<Object> get props => [overspeedEvent, previousState];
}

// BLoC
class RadarBloc extends Bloc<RadarEvent, RadarState> {
  // Utiliser le vrai service radar avec ML Kit au lieu du mock
  final RadarService _radarService = RadarService();
  // Pour revenir au mock, décommentez la ligne suivante et commentez celle du dessus :
  // final MockRadarService _radarService = MockRadarService();
  
  StreamSubscription? _vehicleSubscription;
  StreamSubscription? _overspeedSubscription;
  Timer? _statsTimer;
  
  RadarBloc() : super(RadarInitial()) {
    on<InitializeRadar>(_onInitializeRadar);
    on<StartRadar>(_onStartRadar);
    on<StopRadar>(_onStopRadar);
    on<UpdateSettings>(_onUpdateSettings);
    on<VehiclesUpdated>(_onVehiclesUpdated);
    on<OverspeedDetected>(_onOverspeedDetected);
  }
  
  Future<void> _onInitializeRadar(
    InitializeRadar event,
    Emitter<RadarState> emit,
  ) async {
    emit(RadarInitializing());
    
    try {
      await _radarService.initialize();
      
      // Configurer les subscriptions
      _setupSubscriptions();
      
      emit(RadarInitialized(_radarService.settings));
    } catch (e) {
      emit(RadarError('Erreur d\'initialisation: $e'));
    }
  }
  
  Future<void> _onStartRadar(
    StartRadar event,
    Emitter<RadarState> emit,
  ) async {
    try {
      await _radarService.start();
      
      // Démarrer le timer des statistiques
      _startStatsTimer();
      
      emit(RadarRunning(
        vehicles: [],
        settings: _radarService.settings,
        stats: _radarService.getStats(),
      ));
    } catch (e) {
      emit(RadarError('Erreur de démarrage: $e'));
    }
  }
  
  Future<void> _onStopRadar(
    StopRadar event,
    Emitter<RadarState> emit,
  ) async {
    try {
      await _radarService.stop();
      _statsTimer?.cancel();
      
      emit(RadarStopped(_radarService.settings));
    } catch (e) {
      emit(RadarError('Erreur d\'arrêt: $e'));
    }
  }
  
  void _onUpdateSettings(
    UpdateSettings event,
    Emitter<RadarState> emit,
  ) {
    final newSettings = UserSettings(
      speedLimitKmh: event.speedLimit,
      toleranceKmh: event.tolerance,
      autoSaveEnabled: event.autoSave,
      blurFacesEnabled: event.blurFaces,
      blurPlatesEnabled: event.blurPlates,
      showLicensePlatesEnabled: event.showLicensePlates,
      showDriversEnabled: event.showDrivers,
    );

    _radarService.updateSettings(newSettings);

    if (state is RadarRunning) {
      final currentState = state as RadarRunning;
      emit(currentState.copyWith(settings: newSettings));
    } else if (state is RadarStopped) {
      emit(RadarStopped(newSettings));
    } else if (state is RadarInitialized) {
      emit(RadarInitialized(newSettings));
    }
  }
  
  void _onVehiclesUpdated(
    VehiclesUpdated event,
    Emitter<RadarState> emit,
  ) {
    if (state is RadarRunning) {
      final currentState = state as RadarRunning;
      emit(currentState.copyWith(
        vehicles: event.vehicles,
        stats: _radarService.getStats(),
      ));
    }
  }
  
  void _onOverspeedDetected(
    OverspeedDetected event,
    Emitter<RadarState> emit,
  ) {
    if (state is RadarRunning) {
      final currentState = state as RadarRunning;
      
      // Ajouter à la liste des dépassements récents
      final updatedOverspeeds = List<OverspeedEvent>.from(currentState.recentOverspeeds)
        ..add(event.overspeedEvent);
      
      // Garder seulement les 10 derniers
      if (updatedOverspeeds.length > 10) {
        updatedOverspeeds.removeAt(0);
      }
      
      final updatedState = currentState.copyWith(
        recentOverspeeds: updatedOverspeeds,
      );
      
      // Afficher le modal de dépassement
      emit(OverspeedModalVisible(
        overspeedEvent: event.overspeedEvent,
        previousState: updatedState,
      ));
      
      // Auto-fermer le modal après un délai
      Timer(const Duration(milliseconds: AppConstants.modal_auto_close_ms), () {
        if (state is OverspeedModalVisible) {
          final modalState = state as OverspeedModalVisible;
          emit(modalState.previousState);
        }
      });
    }
  }
  
  void _setupSubscriptions() {
    _vehicleSubscription = _radarService.vehicleStream.listen(
      (vehicles) => add(VehiclesUpdated(vehicles)),
    );
    
    _overspeedSubscription = _radarService.overspeedStream.listen(
      (overspeed) => add(OverspeedDetected(overspeed)),
    );
  }
  
  void _startStatsTimer() {
    _statsTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state is RadarRunning) {
        final currentState = state as RadarRunning;
        emit(currentState.copyWith(stats: _radarService.getStats()));
      }
    });
  }
  
  @override
  Future<void> close() async {
    await _vehicleSubscription?.cancel();
    await _overspeedSubscription?.cancel();
    _statsTimer?.cancel();
    await _radarService.dispose();
    return super.close();
  }
}
