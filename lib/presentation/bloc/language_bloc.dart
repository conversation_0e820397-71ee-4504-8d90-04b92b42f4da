import 'dart:ui';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../core/services/language_service.dart';

// Events
abstract class LanguageEvent extends Equatable {
  const LanguageEvent();
  
  @override
  List<Object> get props => [];
}

class LoadLanguage extends LanguageEvent {}

class ChangeLanguage extends LanguageEvent {
  final String languageCode;
  
  const ChangeLanguage(this.languageCode);
  
  @override
  List<Object> get props => [languageCode];
}

// States
abstract class LanguageState extends Equatable {
  const LanguageState();
  
  @override
  List<Object> get props => [];
}

class LanguageInitial extends LanguageState {}

class LanguageLoading extends LanguageState {}

class LanguageLoaded extends LanguageState {
  final Locale locale;
  final LanguageOption languageOption;
  
  const LanguageLoaded({
    required this.locale,
    required this.languageOption,
  });
  
  @override
  List<Object> get props => [locale, languageOption];
}

class LanguageError extends LanguageState {
  final String message;
  
  const LanguageError(this.message);
  
  @override
  List<Object> get props => [message];
}

// BLoC
class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  LanguageBloc() : super(LanguageInitial()) {
    on<LoadLanguage>(_onLoadLanguage);
    on<ChangeLanguage>(_onChangeLanguage);
  }
  
  Future<void> _onLoadLanguage(
    LoadLanguage event,
    Emitter<LanguageState> emit,
  ) async {
    emit(LanguageLoading());
    
    try {
      final locale = await LanguageService.getSavedLanguage();
      final languageOption = LanguageService.getLanguageByLocale(locale);
      
      if (languageOption != null) {
        emit(LanguageLoaded(
          locale: locale,
          languageOption: languageOption,
        ));
      } else {
        emit(const LanguageError('Language not found'));
      }
    } catch (e) {
      emit(LanguageError('Failed to load language: $e'));
    }
  }
  
  Future<void> _onChangeLanguage(
    ChangeLanguage event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      final languageOption = LanguageService.getLanguageByCode(event.languageCode);
      
      if (languageOption != null) {
        await LanguageService.saveLanguage(event.languageCode);
        
        emit(LanguageLoaded(
          locale: languageOption.locale,
          languageOption: languageOption,
        ));
      } else {
        emit(const LanguageError('Language not supported'));
      }
    } catch (e) {
      emit(LanguageError('Failed to change language: $e'));
    }
  }
}
